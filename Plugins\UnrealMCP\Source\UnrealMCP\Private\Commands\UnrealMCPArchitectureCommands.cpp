#include "Commands/UnrealMCPArchitectureCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// CORREÇÃO CRÍTICA: Includes necessários para criação de Blueprints reais
#include "Engine/Blueprint.h"
#include "Engine/SCS_Node.h"
#include "K2Node.h"
#include "KismetCompiler.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "Components/BoxComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/FloatingPawnMovement.h"
#include "GameFramework/Pawn.h"

// MODERN UE 5.6.1 PCG ELEMENTS - PRODUCTION READY
#include "Elements/PCGStaticMeshSpawner.h"
#include "Elements/PCGSpawnActor.h"
#include "Elements/PCGSurfaceSampler.h"
#include "Elements/PCGTransformPoints.h"
#include "Elements/PCGDensityFilter.h"
#include "Elements/PCGAttributeFilter.h"
#include "MeshSelectors/PCGMeshSelectorWeighted.h"
#include "PCGSettings.h"
#include "PCGNode.h"
#include "PCGGraph.h"
#include "PCGComponent.h"

// Modern UE 5.6.1 Soft Object Path APIs
#include "Engine/AssetManager.h"
#include "UObject/SoftObjectPath.h"

// Modern UE 5.6.1 includes
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Engine/SimpleConstructionScript.h"
#include "Components/ChildActorComponent.h"

// Modern UE 5.6.1 Nanite Support
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "PhysicsEngine/BodySetup.h"

// Modern UE 5.6.1 Performance APIs
#include "HAL/PlatformApplicationMisc.h"
#include "Async/TaskGraphInterfaces.h"

// Experimental UE 5.6.1 PCG includes - Forward declarations for now
namespace UE { namespace PCG { class FPCGContext; } }

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPArchitectureCommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPArchitectureCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("UnrealMCPArchitectureCommands::HandleCommand - Command: %s"), *CommandName);

    if (CommandName == TEXT("create_tower_structures"))
    {
        return HandleCreateTowerStructures(Params);
    }
    else if (CommandName == TEXT("create_base_buildings"))
    {
        return HandleCreateBaseBuildings(Params);
    }
    else if (CommandName == TEXT("create_inhibitor_monuments"))
    {
        return HandleCreateInhibitorMonuments(Params);
    }
    else if (CommandName == TEXT("create_nexus_architecture"))
    {
        return HandleCreateNexusArchitecture(Params);
    }
    else if (CommandName == TEXT("create_defensive_walls"))
    {
        return HandleCreateDefensiveWalls(Params);
    }
    else if (CommandName == TEXT("create_jungle_camps"))
    {
        return HandleCreateJungleCamps(Params);
    }
    else if (CommandName == TEXT("create_minion_spawning_system"))
    {
        return HandleCreateMinionSpawningSystem(Params);
    }
    else if (CommandName == TEXT("create_multilayer_tower_system"))
    {
        return HandleCreateMultilayerTowerSystem(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown architecture command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateTowerStructures(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("tower_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: tower_name"));
    }

    FString TowerName = Params->GetStringField(TEXT("tower_name"));
    FString TowerType = Params->GetStringField(TEXT("tower_type"));
    if (TowerType.IsEmpty()) TowerType = TEXT("basic");
    
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create tower structures using modern UE 5.6.1 APIs
    FAuracronTowerConfig TowerConfig = GetLayerArchitecturalStyle(LayerIndex);
    TowerConfig.TowerName = TowerName;
    TowerConfig.LayerIndex = LayerIndex;
    TowerConfig.TeamIndex = TeamIndex;
    TowerConfig.TowerLocation = Location;

    // Configure tower type-specific properties
    if (TowerType == TEXT("advanced"))
    {
        TowerConfig.TowerHeight = 800.0f;
        TowerConfig.TowerRadius = 150.0f;
        TowerConfig.TowerLevels = 5;
    }
    else if (TowerType == TEXT("nexus"))
    {
        TowerConfig.TowerHeight = 1200.0f;
        TowerConfig.TowerRadius = 200.0f;
        TowerConfig.TowerLevels = 7;
        TowerConfig.bUsePCGGeneration = true;
    }
    else // basic
    {
        TowerConfig.TowerHeight = 500.0f;
        TowerConfig.TowerRadius = 100.0f;
        TowerConfig.TowerLevels = 3;
    }

    // CORREÇÃO CRÍTICA: Criar BLUEPRINT REAL ao invés de apenas JSON
    UBlueprint* CreatedTowerBlueprint = CreateRealTowerBlueprint(TowerConfig);
    AActor* CreatedTower = nullptr;

    if (CreatedTowerBlueprint)
    {
        // Spawn the tower in the world using the created Blueprint
        UWorld* World = GEditor->GetEditorWorldContext().World();
        if (World)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Name = FName(*TowerName);
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
            
            CreatedTower = World->SpawnActor<AActor>(CreatedTowerBlueprint->GeneratedClass, Location, FRotator::ZeroRotator, SpawnParams);
            
            if (CreatedTower)
            {
                CreatedStructures.Add(TowerName, CreatedTower);
                UE_LOG(LogTemp, Log, TEXT("HandleCreateTowerStructures: Tower %s spawned in world at location (%.1f, %.1f, %.1f)"), 
                       *TowerName, Location.X, Location.Y, Location.Z);
            }
        }

        // MANDATORY SAVING - Save the Blueprint asset
        FString BlueprintPath = FString::Printf(TEXT("/Game/Auracron/MOBA/Towers/BP_%s"), *TowerName);
        if (UEditorAssetLibrary::SaveAsset(BlueprintPath))
        {
            UE_LOG(LogTemp, Log, TEXT("HandleCreateTowerStructures: Tower Blueprint %s successfully saved at %s"), *TowerName, *BlueprintPath);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("HandleCreateTowerStructures: Failed to save tower Blueprint %s"), *TowerName);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateTowerStructures: Failed to create tower Blueprint %s"), *TowerName);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_tower_structures"));
    Response->SetStringField(TEXT("tower_name"), TowerName);
    Response->SetStringField(TEXT("tower_type"), TowerType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    Response->SetNumberField(TEXT("tower_height"), TowerConfig.TowerHeight);
    Response->SetNumberField(TEXT("tower_radius"), TowerConfig.TowerRadius);
    Response->SetNumberField(TEXT("tower_levels"), TowerConfig.TowerLevels);
    Response->SetBoolField(TEXT("hierarchical_instancing"), TowerConfig.bUseHierarchicalInstancing);
    Response->SetBoolField(TEXT("pcg_generation"), TowerConfig.bUsePCGGeneration);
    Response->SetBoolField(TEXT("success"), CreatedTower != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add location info
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateTowerStructures: Created tower %s (Type: %s, Layer: %d, Team: %d, Height: %.1f)"),
           *TowerName, *TowerType, LayerIndex, TeamIndex, TowerConfig.TowerHeight);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateBaseBuildings(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("building_name")) || !Params->HasField(TEXT("building_type")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: building_name, building_type"));
    }

    FString BuildingName = Params->GetStringField(TEXT("building_name"));
    FString BuildingType = Params->GetStringField(TEXT("building_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create base buildings using modern UE 5.6.1 APIs
    AActor* CreatedBuilding = CreateHierarchicalBuilding(BuildingName, BuildingType, Location, LayerIndex);
    
    if (CreatedBuilding)
    {
        CreatedStructures.Add(BuildingName, CreatedBuilding);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_base_buildings"));
    Response->SetStringField(TEXT("building_name"), BuildingName);
    Response->SetStringField(TEXT("building_type"), BuildingType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), CreatedBuilding != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add location info
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), Location.X);
    LocationResponse->SetNumberField(TEXT("y"), Location.Y);
    LocationResponse->SetNumberField(TEXT("z"), Location.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateBaseBuildings: Created building %s (Type: %s, Layer: %d)"),
           *BuildingName, *BuildingType, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateInhibitorMonuments(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("monument_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: monument_name"));
    }

    FString MonumentName = Params->GetStringField(TEXT("monument_name"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create inhibitor monuments using modern UE 5.6.1 APIs
    FAuracronTowerConfig MonumentConfig = GetLayerArchitecturalStyle(LayerIndex);
    MonumentConfig.TowerName = MonumentName;
    MonumentConfig.LayerIndex = LayerIndex;
    MonumentConfig.TeamIndex = TeamIndex;
    MonumentConfig.TowerLocation = Location;
    MonumentConfig.TowerHeight = 600.0f;
    MonumentConfig.TowerRadius = 120.0f;
    MonumentConfig.TowerLevels = 1; // Monuments are typically single structures
    MonumentConfig.bUsePCGGeneration = true; // Use PCG for unique designs

    AActor* CreatedMonument = CreateRobustTowerStructure(MonumentConfig);
    
    if (CreatedMonument)
    {
        CreatedStructures.Add(MonumentName, CreatedMonument);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_inhibitor_monuments"));
    Response->SetStringField(TEXT("monument_name"), MonumentName);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    Response->SetBoolField(TEXT("success"), CreatedMonument != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateInhibitorMonuments: Created monument %s for layer %d, team %d"),
           *MonumentName, LayerIndex, TeamIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateNexusArchitecture(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("nexus_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: nexus_name"));
    }

    FString NexusName = Params->GetStringField(TEXT("nexus_name"));
    int32 TeamIndex = Params->GetIntegerField(TEXT("team_index"));
    int32 Complexity = Params->GetIntegerField(TEXT("complexity"));
    if (Complexity <= 0) Complexity = 5;

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create nexus architecture using modern UE 5.6.1 APIs
    FAuracronTowerConfig NexusConfig = GetLayerArchitecturalStyle(0); // Nexus is always on ground layer
    NexusConfig.TowerName = NexusName;
    NexusConfig.LayerIndex = 0;
    NexusConfig.TeamIndex = TeamIndex;
    NexusConfig.TowerLocation = Location;
    NexusConfig.TowerHeight = 1500.0f + (Complexity * 100.0f);
    NexusConfig.TowerRadius = 300.0f + (Complexity * 50.0f);
    NexusConfig.TowerLevels = 5 + Complexity;
    NexusConfig.bUsePCGGeneration = true; // Always use PCG for nexus complexity
    NexusConfig.bUseHierarchicalInstancing = true;

    AActor* CreatedNexus = CreateRobustTowerStructure(NexusConfig);

    if (CreatedNexus)
    {
        CreatedStructures.Add(NexusName, CreatedNexus);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_nexus_architecture"));
    Response->SetStringField(TEXT("nexus_name"), NexusName);
    Response->SetNumberField(TEXT("team_index"), TeamIndex);
    Response->SetNumberField(TEXT("complexity"), Complexity);
    Response->SetNumberField(TEXT("nexus_height"), NexusConfig.TowerHeight);
    Response->SetNumberField(TEXT("nexus_radius"), NexusConfig.TowerRadius);
    Response->SetNumberField(TEXT("nexus_levels"), NexusConfig.TowerLevels);
    Response->SetBoolField(TEXT("success"), CreatedNexus != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateNexusArchitecture: Created nexus %s for team %d (Complexity: %d, Height: %.1f)"),
           *NexusName, TeamIndex, Complexity, NexusConfig.TowerHeight);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateDefensiveWalls(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("wall_name")) || !Params->HasField(TEXT("wall_points")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: wall_name, wall_points"));
    }

    FString WallName = Params->GetStringField(TEXT("wall_name"));
    float WallHeight = Params->GetNumberField(TEXT("wall_height"));
    if (WallHeight <= 0.0f) WallHeight = 400.0f;
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse wall points from JSON
    TArray<FVector> WallPoints;
    const TArray<TSharedPtr<FJsonValue>>* PointsArray;
    if (Params->TryGetArrayField(TEXT("wall_points"), PointsArray))
    {
        for (const auto& PointValue : *PointsArray)
        {
            const TSharedPtr<FJsonObject>& PointObj = PointValue->AsObject();
            if (PointObj.IsValid())
            {
                FVector Point;
                Point.X = PointObj->GetNumberField(TEXT("x"));
                Point.Y = PointObj->GetNumberField(TEXT("y"));
                Point.Z = PointObj->GetNumberField(TEXT("z"));
                WallPoints.Add(Point);
            }
        }
    }

    // STEP 2: REAL IMPLEMENTATION - Create defensive walls using modern UE 5.6.1 APIs
    FSplineStructureConfig WallConfig;
    WallConfig.StructureName = WallName;
    WallConfig.SplinePoints = WallPoints;
    WallConfig.StructureWidth = 100.0f; // Wall thickness
    WallConfig.StructureHeight = WallHeight;
    WallConfig.SplineSegments = WallPoints.Num() * 2; // More segments for smoother walls
    WallConfig.bClosedLoop = false;

    AActor* CreatedWall = CreateSplineBasedStructure(WallConfig);

    if (CreatedWall)
    {
        CreatedStructures.Add(WallName, CreatedWall);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_defensive_walls"));
    Response->SetStringField(TEXT("wall_name"), WallName);
    Response->SetNumberField(TEXT("wall_height"), WallHeight);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("wall_points_count"), WallPoints.Num());
    Response->SetNumberField(TEXT("spline_segments"), WallConfig.SplineSegments);
    Response->SetBoolField(TEXT("success"), CreatedWall != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateDefensiveWalls: Created wall %s with %d points (Height: %.1f, Layer: %d)"),
           *WallName, WallPoints.Num(), WallHeight, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateJungleCamps(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("camp_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: camp_name"));
    }

    FString CampName = Params->GetStringField(TEXT("camp_name"));
    FString CampType = Params->GetStringField(TEXT("camp_type"));
    if (CampType.IsEmpty()) CampType = TEXT("neutral");
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // Parse location from JSON
    FVector Location = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        Location.X = (*LocationObj)->GetNumberField(TEXT("x"));
        Location.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        Location.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create jungle camps using modern UE 5.6.1 APIs
    FAuracronTowerConfig CampConfig = GetLayerArchitecturalStyle(LayerIndex);
    CampConfig.TowerName = CampName;
    CampConfig.LayerIndex = LayerIndex;
    CampConfig.TowerLocation = Location;

    // Configure camp type-specific properties
    if (CampType == TEXT("epic"))
    {
        CampConfig.TowerHeight = 400.0f;
        CampConfig.TowerRadius = 200.0f;
        CampConfig.TowerLevels = 2;
        CampConfig.bUsePCGGeneration = true;
    }
    else if (CampType == TEXT("boss"))
    {
        CampConfig.TowerHeight = 600.0f;
        CampConfig.TowerRadius = 300.0f;
        CampConfig.TowerLevels = 3;
        CampConfig.bUsePCGGeneration = true;
    }
    else // neutral
    {
        CampConfig.TowerHeight = 200.0f;
        CampConfig.TowerRadius = 100.0f;
        CampConfig.TowerLevels = 1;
        CampConfig.bUsePCGGeneration = false;
    }

    AActor* CreatedCamp = CreateRobustTowerStructure(CampConfig);

    if (CreatedCamp)
    {
        CreatedStructures.Add(CampName, CreatedCamp);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_jungle_camps"));
    Response->SetStringField(TEXT("camp_name"), CampName);
    Response->SetStringField(TEXT("camp_type"), CampType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), CreatedCamp != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateJungleCamps: Created camp %s (Type: %s, Layer: %d)"),
           *CampName, *CampType, LayerIndex);

    return Response;
}

// ========================================
// ROBUST ARCHITECTURE CREATION - MODERN UE 5.6.1 APIS
// ========================================

AActor* UUnrealMCPArchitectureCommands::CreateRobustTowerStructure(const FAuracronTowerConfig& TowerConfig)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Must be called from game thread"));
        return nullptr;
    }

    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: No valid world context"));
        return nullptr;
    }

    // MEMORY LEAK PREVENTION - Validate config before proceeding
    if (TowerConfig.TowerName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Invalid tower name - preventing memory leak"));
        return nullptr;
    }

    // STEP 1: Create tower actor using modern UE 5.6.1 APIs with enhanced spawn parameters
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*TowerConfig.TowerName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bDeferConstruction = false; // Immediate construction for stability
    SpawnParams.ObjectFlags = RF_Transactional; // Enable undo/redo support

    AActor* TowerActor = World->SpawnActor<AActor>(AActor::StaticClass(), TowerConfig.TowerLocation, TowerConfig.TowerRotation, SpawnParams);
    if (!TowerActor || !IsValid(TowerActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Failed to spawn tower actor - memory leak prevented"));
        return nullptr;
    }

    // Configure actor with modern UE 5.6.1 settings
    TowerActor->SetActorLabel(TowerConfig.TowerName);
    TowerActor->SetActorEnableCollision(true);
    TowerActor->SetCanBeDamaged(true); // Enable for MOBA gameplay

    // STEP 2: Create hierarchical instanced static mesh component using modern UE 5.6.1 APIs
    if (TowerConfig.bUseHierarchicalInstancing)
    {
        UHierarchicalInstancedStaticMeshComponent* HISMComponent = NewObject<UHierarchicalInstancedStaticMeshComponent>(TowerActor);
        if (!HISMComponent || !IsValid(HISMComponent))
        {
            UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerStructure: Failed to create HierarchicalInstancedStaticMeshComponent"));
            return nullptr;
        }

        // Configure HISM component using modern UE 5.6.1 settings
        HISMComponent->SetupAttachment(TowerActor->GetRootComponent());
        HISMComponent->SetCastShadow(true);
        HISMComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        HISMComponent->SetCollisionProfileName(TEXT("BlockAll"));

        // Configure modern UE 5.6.1 HISM settings with Nanite support
        HISMComponent->SetCullDistances(0, 10000); // No culling for important structures
        HISMComponent->bUseAsOccluder = true;
        HISMComponent->bEnableAutoLODGeneration = true;

        // Modern UE 5.6.1 Performance optimizations
        HISMComponent->bSortTriangles = true;
        HISMComponent->bUseDefaultCollision = true;

        // Enable modern UE 5.6.1 features for better performance
        HISMComponent->bReceivesDecals = true;
        HISMComponent->bUseAttachParentBound = false;

        // Create tower levels using hierarchical instancing
        for (int32 Level = 0; Level < TowerConfig.TowerLevels; Level++)
        {
            float LevelHeight = (TowerConfig.TowerHeight / TowerConfig.TowerLevels) * Level;
            float LevelScale = 1.0f - (Level * 0.1f); // Each level slightly smaller

            FTransform LevelTransform;
            LevelTransform.SetLocation(FVector(0, 0, LevelHeight));
            LevelTransform.SetScale3D(FVector(LevelScale, LevelScale, 1.0f));

            HISMComponent->AddInstance(LevelTransform);
        }

        // Add component to actor
        TowerActor->AddInstanceComponent(HISMComponent);
        HISMComponent->RegisterComponent();

        // Cache the HISM component
        HierarchicalComponents.Add(TowerConfig.TowerName, HISMComponent);
    }
    else
    {
        // STEP 3: Create individual static mesh components for each level
        for (int32 Level = 0; Level < TowerConfig.TowerLevels; Level++)
        {
            UStaticMeshComponent* LevelComponent = NewObject<UStaticMeshComponent>(TowerActor);
            if (!LevelComponent || !IsValid(LevelComponent))
            {
                continue;
            }

            float LevelHeight = (TowerConfig.TowerHeight / TowerConfig.TowerLevels) * Level;
            float LevelScale = 1.0f - (Level * 0.1f);

            LevelComponent->SetupAttachment(TowerActor->GetRootComponent());
            LevelComponent->SetRelativeLocation(FVector(0, 0, LevelHeight));
            LevelComponent->SetRelativeScale3D(FVector(LevelScale, LevelScale, 1.0f));
            LevelComponent->SetCastShadow(true);
            LevelComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

            TowerActor->AddInstanceComponent(LevelComponent);
            LevelComponent->RegisterComponent();
        }
    }

    // STEP 4: Setup PCG generation if requested
    if (TowerConfig.bUsePCGGeneration)
    {
        UActorComponent* PCGComponent = SetupPCGGeneration(TowerConfig.TowerName, TowerConfig.LayerIndex);
        if (PCGComponent)
        {
            TowerActor->AddInstanceComponent(PCGComponent);
            PCGComponent->RegisterComponent();
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustTowerStructure: Created tower %s with %d levels (Height: %.1f, HISM: %s, PCG: %s)"),
           *TowerConfig.TowerName, TowerConfig.TowerLevels, TowerConfig.TowerHeight,
           TowerConfig.bUseHierarchicalInstancing ? TEXT("Yes") : TEXT("No"),
           TowerConfig.bUsePCGGeneration ? TEXT("Yes") : TEXT("No"));

    return TowerActor;
}

AActor* UUnrealMCPArchitectureCommands::CreateHierarchicalBuilding(const FString& BuildingName, const FString& BuildingType, const FVector& Location, int32 LayerIndex)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateHierarchicalBuilding: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create building actor using modern UE 5.6.1 APIs with UNIQUE NAME GENERATION
    
    // ROBUST FIX: Generate unique name to prevent "Cannot generate unique name" fatal error
    FString UniqueBuildingName = GenerateUniqueActorName(World, BuildingName);
    if (UniqueBuildingName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateHierarchicalBuilding: Could not generate unique name for: %s"), *BuildingName);
        return nullptr;
    }
    
    UE_LOG(LogTemp, Log, TEXT("CreateHierarchicalBuilding: Using unique name: %s (original: %s)"), *UniqueBuildingName, *BuildingName);
    
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*UniqueBuildingName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* BuildingActor = World->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!BuildingActor || !IsValid(BuildingActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateHierarchicalBuilding: Failed to spawn building actor"));
        return nullptr;
    }

    BuildingActor->SetActorLabel(UniqueBuildingName);

    // STEP 2: Create building elements using modular architecture
    TArray<FBuildingElementConfig> BuildingElements;

    // Configure building type-specific elements
    if (BuildingType == TEXT("barracks"))
    {
        // Create barracks elements
        FBuildingElementConfig Foundation;
        Foundation.ElementName = TEXT("Foundation");
        Foundation.ElementType = TEXT("foundation");
        Foundation.ElementScale = FVector(4.0f, 4.0f, 0.5f);
        BuildingElements.Add(Foundation);

        FBuildingElementConfig Walls;
        Walls.ElementName = TEXT("Walls");
        Walls.ElementType = TEXT("wall");
        Walls.ElementScale = FVector(4.0f, 4.0f, 2.0f);
        Walls.ElementTransform.SetLocation(FVector(0, 0, 100));
        BuildingElements.Add(Walls);

        FBuildingElementConfig Roof;
        Roof.ElementName = TEXT("Roof");
        Roof.ElementType = TEXT("roof");
        Roof.ElementScale = FVector(4.2f, 4.2f, 0.3f);
        Roof.ElementTransform.SetLocation(FVector(0, 0, 300));
        BuildingElements.Add(Roof);
    }
    else if (BuildingType == TEXT("shop"))
    {
        // Create shop elements
        FBuildingElementConfig Foundation;
        Foundation.ElementName = TEXT("Foundation");
        Foundation.ElementType = TEXT("foundation");
        Foundation.ElementScale = FVector(3.0f, 3.0f, 0.5f);
        BuildingElements.Add(Foundation);

        FBuildingElementConfig Counter;
        Counter.ElementName = TEXT("Counter");
        Counter.ElementType = TEXT("pillar");
        Counter.ElementScale = FVector(2.0f, 1.0f, 1.5f);
        Counter.ElementTransform.SetLocation(FVector(0, 0, 75));
        BuildingElements.Add(Counter);
    }
    else // fountain or default
    {
        // Create fountain elements
        FBuildingElementConfig Base;
        Base.ElementName = TEXT("Base");
        Base.ElementType = TEXT("foundation");
        Base.ElementScale = FVector(2.0f, 2.0f, 1.0f);
        BuildingElements.Add(Base);

        FBuildingElementConfig Pillar;
        Pillar.ElementName = TEXT("Pillar");
        Pillar.ElementType = TEXT("pillar");
        Pillar.ElementScale = FVector(0.5f, 0.5f, 3.0f);
        Pillar.ElementTransform.SetLocation(FVector(0, 0, 150));
        BuildingElements.Add(Pillar);
    }

    // STEP 3: Create modular architecture using construction script
    AActor* ModularBuilding = CreateModularArchitecture(BuildingName, BuildingElements, Location);
    if (ModularBuilding)
    {
        // Transfer components from modular building to main building actor using modern UE 5.6.1 API
        TArray<UActorComponent*> Components;
        ModularBuilding->GetComponents<UActorComponent>(Components);
        for (UActorComponent* Component : Components)
        {
            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                BuildingActor->AddInstanceComponent(MeshComp);
                MeshComp->RegisterComponent();
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CreateHierarchicalBuilding: Created building %s (Type: %s, Layer: %d, Elements: %d)"),
           *BuildingName, *BuildingType, LayerIndex, BuildingElements.Num());

    return BuildingActor;
}

AActor* UUnrealMCPArchitectureCommands::CreateSplineBasedStructure(const FSplineStructureConfig& SplineConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: No valid world context"));
        return nullptr;
    }

    if (SplineConfig.SplinePoints.Num() < 2)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: Need at least 2 spline points"));
        return nullptr;
    }

    // STEP 1: Create spline actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*SplineConfig.StructureName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* SplineActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
    if (!SplineActor || !IsValid(SplineActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: Failed to spawn spline actor"));
        return nullptr;
    }

    SplineActor->SetActorLabel(SplineConfig.StructureName);

    // STEP 2: Create spline component using modern UE 5.6.1 APIs
    USplineComponent* SplineComponent = NewObject<USplineComponent>(SplineActor);
    if (!SplineComponent || !IsValid(SplineComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateSplineBasedStructure: Failed to create SplineComponent"));
        return nullptr;
    }

    // Configure spline component using modern settings
    SplineComponent->SetupAttachment(SplineActor->GetRootComponent());
    SplineComponent->ClearSplinePoints();

    // Add spline points
    for (int32 i = 0; i < SplineConfig.SplinePoints.Num(); i++)
    {
        SplineComponent->AddSplinePoint(SplineConfig.SplinePoints[i], ESplineCoordinateSpace::World);
    }

    // Configure spline settings
    SplineComponent->SetClosedLoop(SplineConfig.bClosedLoop);
    SplineComponent->UpdateSpline();

    // Add component to actor
    SplineActor->AddInstanceComponent(SplineComponent);
    SplineComponent->RegisterComponent();

    // STEP 3: Create spline mesh components along the spline
    int32 NumSegments = SplineComponent->GetNumberOfSplineSegments();
    for (int32 SegmentIndex = 0; SegmentIndex < NumSegments; SegmentIndex++)
    {
        USplineMeshComponent* SplineMeshComponent = NewObject<USplineMeshComponent>(SplineActor);
        if (!SplineMeshComponent || !IsValid(SplineMeshComponent))
        {
            continue;
        }

        // Configure spline mesh component
        SplineMeshComponent->SetupAttachment(SplineComponent);
        SplineMeshComponent->SetCastShadow(true);
        SplineMeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        SplineMeshComponent->SetCollisionProfileName(TEXT("BlockAll"));

        // Set spline mesh start and end points
        FVector StartPos, StartTangent, EndPos, EndTangent;
        SplineComponent->GetLocationAndTangentAtSplinePoint(SegmentIndex, StartPos, StartTangent, ESplineCoordinateSpace::Local);
        SplineComponent->GetLocationAndTangentAtSplinePoint(SegmentIndex + 1, EndPos, EndTangent, ESplineCoordinateSpace::Local);

        SplineMeshComponent->SetStartAndEnd(StartPos, StartTangent, EndPos, EndTangent);

        // Configure spline mesh scale
        FVector2D StartScale(SplineConfig.StructureWidth / 100.0f, SplineConfig.StructureHeight / 100.0f);
        FVector2D EndScale = StartScale;
        SplineMeshComponent->SetStartScale(StartScale);
        SplineMeshComponent->SetEndScale(EndScale);

        // Add component to actor
        SplineActor->AddInstanceComponent(SplineMeshComponent);
        SplineMeshComponent->RegisterComponent();
    }

    // Cache the spline component
    SplineComponents.Add(SplineConfig.StructureName, SplineComponent);

    UE_LOG(LogTemp, Log, TEXT("CreateSplineBasedStructure: Created spline structure %s with %d points and %d segments"),
           *SplineConfig.StructureName, SplineConfig.SplinePoints.Num(), NumSegments);

    return SplineActor;
}

AActor* UUnrealMCPArchitectureCommands::CreateModularArchitecture(const FString& ArchitectureName, const TArray<FBuildingElementConfig>& Elements, const FVector& Location)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateModularArchitecture: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create modular architecture actor using modern UE 5.6.1 APIs with UNIQUE NAME GENERATION
    
    // ROBUST FIX: Generate unique name to prevent "Cannot generate unique name" fatal error
    FString UniqueArchitectureName = GenerateUniqueActorName(World, ArchitectureName);
    if (UniqueArchitectureName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateModularArchitecture: Could not generate unique name for: %s"), *ArchitectureName);
        return nullptr;
    }
    
    UE_LOG(LogTemp, Log, TEXT("CreateModularArchitecture: Using unique name: %s (original: %s)"), *UniqueArchitectureName, *ArchitectureName);
    
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*UniqueArchitectureName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* ArchitectureActor = World->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!ArchitectureActor || !IsValid(ArchitectureActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateModularArchitecture: Failed to spawn architecture actor"));
        return nullptr;
    }

    ArchitectureActor->SetActorLabel(UniqueArchitectureName);

    // STEP 2: Create construction script using modern UE 5.6.1 APIs
    USimpleConstructionScript* ConstructionScript = NewObject<USimpleConstructionScript>(ArchitectureActor);
    if (!ConstructionScript || !IsValid(ConstructionScript))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateModularArchitecture: Failed to create SimpleConstructionScript"));
        return nullptr;
    }

    // STEP 3: Create building elements using construction script
    for (const FBuildingElementConfig& Element : Elements)
    {
        UStaticMeshComponent* ElementComponent = NewObject<UStaticMeshComponent>(ArchitectureActor);
        if (!ElementComponent || !IsValid(ElementComponent))
        {
            continue;
        }

        // Configure element component
        ElementComponent->SetupAttachment(ArchitectureActor->GetRootComponent());
        ElementComponent->SetRelativeTransform(Element.ElementTransform);
        ElementComponent->SetRelativeScale3D(Element.ElementScale);
        ElementComponent->SetCastShadow(true);
        ElementComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        ElementComponent->SetCollisionProfileName(TEXT("BlockAll"));

        // Add component to actor
        ArchitectureActor->AddInstanceComponent(ElementComponent);
        ElementComponent->RegisterComponent();
    }

    // Cache the construction script
    ConstructionScripts.Add(ArchitectureName, ConstructionScript);

    UE_LOG(LogTemp, Log, TEXT("CreateModularArchitecture: Created modular architecture %s with %d elements"),
           *ArchitectureName, Elements.Num());

    return ArchitectureActor;
}

UActorComponent* UUnrealMCPArchitectureCommands::SetupPCGGeneration(const FString& StructureName, int32 LayerIndex)
{
    // STEP 1: THREAD SAFETY VALIDATION - MODERN UE 5.6.1
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SetupPCGGeneration must execute on Game Thread"));
        return nullptr;
    }

    // STEP 2: CREATE PCG COMPONENT USING MODERN UE 5.6.1 APIs - PRODUCTION READY
    UPCGComponent* PCGComponent = NewObject<UPCGComponent>();
    if (!PCGComponent || !IsValid(PCGComponent) || PCGComponent->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create UPCGComponent for structure %s"), *StructureName);
        return nullptr;
    }

    // STEP 3: GET PCG SUBSYSTEM FOR MODERN UE 5.6.1 INTEGRATION
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World) || World->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid world for PCG setup"));
        return nullptr;
    }

    UPCGSubsystem* PCGSubsystem = World->GetSubsystem<UPCGSubsystem>();
    if (!PCGSubsystem || !IsValid(PCGSubsystem) || PCGSubsystem->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: PCG Subsystem not available"));
        return nullptr;
    }

    // STEP 4: CREATE PCG GRAPH FOR AURACRON LAYER-SPECIFIC GENERATION - MODERN UE 5.6.1
    UPCGGraph* PCGGraph = NewObject<UPCGGraph>(PCGComponent);
    if (!PCGGraph || !IsValid(PCGGraph) || PCGGraph->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create PCG Graph"));
        return nullptr;
    }

    // STEP 5: CONFIGURE PCG GRAPH FOR AURACRON LAYERS - PRODUCTION READY
    try
    {
        // Set graph name based on structure and layer using modern UE 5.6.1 API
        FString GraphName = FString::Printf(TEXT("Auracron_%s_Layer%d_PCG"), *StructureName, LayerIndex);
        PCGGraph->Rename(*GraphName, nullptr, REN_DontCreateRedirectors | REN_ForceNoResetLoaders);

        // Configure layer-specific generation parameters
        switch (LayerIndex)
        {
            case 0: // Planície Radiante
                ConfigurePCGForPlanicieRadiante(PCGGraph, StructureName);
                break;
            case 1: // Firmamento Zephyr
                ConfigurePCGForFirmamentoZephyr(PCGGraph, StructureName);
                break;
            case 2: // Abismo Umbral
                ConfigurePCGForAbismoUmbral(PCGGraph, StructureName);
                break;
            default:
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: Unknown layer index %d, using default PCG configuration"), LayerIndex);
                ConfigurePCGDefault(PCGGraph, StructureName);
                break;
        }

        // Assign graph to component
        PCGComponent->SetGraph(PCGGraph);

        // Configure PCG component properties for Auracron
        PCGComponent->SetGenerationGridSize(1024); // Optimized for Auracron scale
        PCGComponent->bActivated = true;
        PCGComponent->bGenerated = false; // Will be generated on demand

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully created PCG component for structure %s (Layer: %d) with graph %s"),
               *StructureName, LayerIndex, *GraphName);
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring PCG component for structure %s"), *StructureName);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("SetupPCGGeneration: Created PCG component for structure %s (Layer: %d)"),
           *StructureName, LayerIndex);

    return PCGComponent;
}

// MODERN UE 5.6.1 PCG CONFIGURATION FUNCTIONS - PRODUCTION READY
void UUnrealMCPArchitectureCommands::ConfigurePCGForPlanicieRadiante(UPCGGraph* PCGGraph, const FString& StructureName)
{
    if (!PCGGraph || !IsValid(PCGGraph) || PCGGraph->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid PCG Graph for Planície Radiante"));
        return;
    }

    // ROBUST CONFIGURATION FOR PLANÍCIE RADIANTE (Golden Plains) - PRODUCTION READY
    try
    {
        // Set generation parameters for divine/golden structures using modern UE 5.6.1 API
        FString GraphName = FString::Printf(TEXT("PlanicieRadiante_%s"), *StructureName);
        PCGGraph->Rename(*GraphName, nullptr, REN_DontCreateRedirectors | REN_ForceNoResetLoaders);

        // STEP 1: CREATE SURFACE SAMPLER NODE FOR GOLDEN TERRAIN
        UPCGSurfaceSamplerSettings* SurfaceSamplerSettings = nullptr;
        UPCGNode* SurfaceSamplerNode = PCGGraph->AddNodeOfType<UPCGSurfaceSamplerSettings>(SurfaceSamplerSettings);
        if (SurfaceSamplerNode && SurfaceSamplerSettings)
        {
            SurfaceSamplerSettings->PointsPerSquaredMeter = 2.0f; // Dense sampling for divine structures
            SurfaceSamplerSettings->PointExtents = FVector(50.0f, 50.0f, 100.0f); // Tall divine structures
            SurfaceSamplerSettings->bUnbounded = false;
        }

        // STEP 2: CREATE DENSITY FILTER FOR GOLDEN STRUCTURES DISTRIBUTION
        UPCGDensityFilterSettings* DensityFilterSettings = nullptr;
        UPCGNode* DensityFilterNode = PCGGraph->AddNodeOfType<UPCGDensityFilterSettings>(DensityFilterSettings);
        if (DensityFilterNode && DensityFilterSettings)
        {
            DensityFilterSettings->LowerBound = 0.7f; // High density for divine realm
            DensityFilterSettings->UpperBound = 1.0f;
            DensityFilterSettings->bInvertFilter = false;
        }

        // STEP 3: CREATE TRANSFORM POINTS FOR DIVINE SCALING
        UPCGTransformPointsSettings* TransformSettings = nullptr;
        UPCGNode* TransformNode = PCGGraph->AddNodeOfType<UPCGTransformPointsSettings>(TransformSettings);
        if (TransformNode && TransformSettings)
        {
            TransformSettings->bApplyToAttribute = false;
            TransformSettings->OffsetMin = FVector(0.0f, 0.0f, 0.0f);
            TransformSettings->OffsetMax = FVector(0.0f, 0.0f, 200.0f); // Elevated divine structures
            TransformSettings->RotationMin = FRotator(0.0f, 0.0f, 0.0f);
            TransformSettings->RotationMax = FRotator(0.0f, 0.0f, 360.0f);
            TransformSettings->ScaleMin = FVector(1.2f, 1.2f, 1.5f); // Larger, taller divine structures
            TransformSettings->ScaleMax = FVector(2.0f, 2.0f, 3.0f);
        }

        // STEP 4: CREATE STATIC MESH SPAWNER FOR GOLDEN ARCHITECTURE
        UPCGStaticMeshSpawnerSettings* MeshSpawnerSettings = nullptr;
        UPCGNode* MeshSpawnerNode = PCGGraph->AddNodeOfType<UPCGStaticMeshSpawnerSettings>(MeshSpawnerSettings);
        if (MeshSpawnerNode && MeshSpawnerSettings)
        {
            // Set mesh selector type to weighted
            MeshSpawnerSettings->MeshSelectorType = UPCGMeshSelectorWeighted::StaticClass();

            // Create weighted mesh selector
            UPCGMeshSelectorWeighted* WeightedSelector = NewObject<UPCGMeshSelectorWeighted>(MeshSpawnerSettings);
            if (WeightedSelector)
            {
                // Load golden/divine meshes for Planície Radiante using modern UE 5.6 APIs
                FString GoldenMeshPath = FString::Printf(TEXT("/Game/Auracron/Meshes/Divine/%s_Golden"), *StructureName);
                FSoftObjectPath GoldenMeshSoftPath(GoldenMeshPath);
                TSoftObjectPtr<UStaticMesh> GoldenMesh(GoldenMeshSoftPath);

                if (!GoldenMesh.IsNull())
                {
                    WeightedSelector->MeshEntries.Add(FPCGMeshSelectorWeightedEntry(GoldenMesh, 100)); // 100% weight for golden mesh
                }
                else
                {
                    // FALLBACK: Use default golden cube if custom mesh not found
                    FSoftObjectPath DefaultMeshSoftPath(TEXT("/Engine/BasicShapes/Cube"));
                    TSoftObjectPtr<UStaticMesh> DefaultMesh(DefaultMeshSoftPath);
                    WeightedSelector->MeshEntries.Add(FPCGMeshSelectorWeightedEntry(DefaultMesh, 100));
                }

                MeshSpawnerSettings->MeshSelectorParameters = WeightedSelector;
            }

            MeshSpawnerSettings->bApplyMeshBoundsToPoints = true;
        }

        // STEP 5: CONNECT NODES IN PIPELINE - MODERN UE 5.6.1 API
        if (SurfaceSamplerNode && DensityFilterNode)
        {
            PCGGraph->AddEdge(SurfaceSamplerNode, TEXT("Out"), DensityFilterNode, TEXT("In"));
        }
        if (DensityFilterNode && TransformNode)
        {
            PCGGraph->AddEdge(DensityFilterNode, TEXT("Out"), TransformNode, TEXT("In"));
        }
        if (TransformNode && MeshSpawnerNode)
        {
            PCGGraph->AddEdge(TransformNode, TEXT("Out"), MeshSpawnerNode, TEXT("In"));
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: FULLY CONFIGURED PCG for Planície Radiante - %s with %d nodes"),
               *StructureName, PCGGraph->GetNodes().Num());
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring PCG for Planície Radiante"));
    }
}

void UUnrealMCPArchitectureCommands::ConfigurePCGForFirmamentoZephyr(UPCGGraph* PCGGraph, const FString& StructureName)
{
    if (!PCGGraph || !IsValid(PCGGraph) || PCGGraph->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid PCG Graph for Firmamento Zephyr"));
        return;
    }

    // ROBUST CONFIGURATION FOR FIRMAMENTO ZEPHYR (Sky Realm) - PRODUCTION READY
    try
    {
        // Set generation parameters for aerial/wind structures using modern UE 5.6.1 API
        FString GraphName = FString::Printf(TEXT("FirmamentoZephyr_%s"), *StructureName);
        PCGGraph->Rename(*GraphName, nullptr, REN_DontCreateRedirectors | REN_ForceNoResetLoaders);

        // STEP 1: CREATE SURFACE SAMPLER NODE FOR AERIAL TERRAIN
        UPCGSurfaceSamplerSettings* SurfaceSamplerSettings = nullptr;
        UPCGNode* SurfaceSamplerNode = PCGGraph->AddNodeOfType<UPCGSurfaceSamplerSettings>(SurfaceSamplerSettings);
        if (SurfaceSamplerNode && SurfaceSamplerSettings)
        {
            SurfaceSamplerSettings->PointsPerSquaredMeter = 1.5f; // Moderate sampling for floating structures
            SurfaceSamplerSettings->PointExtents = FVector(75.0f, 75.0f, 150.0f); // Large ethereal structures
            SurfaceSamplerSettings->bUnbounded = true; // Unbounded for sky realm
        }

        // STEP 2: CREATE DENSITY FILTER FOR ETHEREAL STRUCTURES DISTRIBUTION
        UPCGDensityFilterSettings* DensityFilterSettings = nullptr;
        UPCGNode* DensityFilterNode = PCGGraph->AddNodeOfType<UPCGDensityFilterSettings>(DensityFilterSettings);
        if (DensityFilterNode && DensityFilterSettings)
        {
            DensityFilterSettings->LowerBound = 0.4f; // Lower density for floating structures
            DensityFilterSettings->UpperBound = 0.8f;
            DensityFilterSettings->bInvertFilter = false;
        }

        // STEP 3: CREATE TRANSFORM POINTS FOR AERIAL POSITIONING
        UPCGTransformPointsSettings* TransformSettings = nullptr;
        UPCGNode* TransformNode = PCGGraph->AddNodeOfType<UPCGTransformPointsSettings>(TransformSettings);
        if (TransformNode && TransformSettings)
        {
            TransformSettings->bApplyToAttribute = false;
            TransformSettings->OffsetMin = FVector(-100.0f, -100.0f, 500.0f); // High altitude floating
            TransformSettings->OffsetMax = FVector(100.0f, 100.0f, 1500.0f);
            TransformSettings->RotationMin = FRotator(-15.0f, 0.0f, 0.0f); // Slight wind tilt
            TransformSettings->RotationMax = FRotator(15.0f, 0.0f, 360.0f);
            TransformSettings->ScaleMin = FVector(0.8f, 0.8f, 1.0f); // Ethereal, lighter structures
            TransformSettings->ScaleMax = FVector(1.5f, 1.5f, 2.5f);
        }

        // STEP 4: CREATE STATIC MESH SPAWNER FOR ETHEREAL ARCHITECTURE
        UPCGStaticMeshSpawnerSettings* MeshSpawnerSettings = nullptr;
        UPCGNode* MeshSpawnerNode = PCGGraph->AddNodeOfType<UPCGStaticMeshSpawnerSettings>(MeshSpawnerSettings);
        if (MeshSpawnerNode && MeshSpawnerSettings)
        {
            // Set mesh selector type to weighted
            MeshSpawnerSettings->MeshSelectorType = UPCGMeshSelectorWeighted::StaticClass();

            // Create weighted mesh selector
            UPCGMeshSelectorWeighted* WeightedSelector = NewObject<UPCGMeshSelectorWeighted>(MeshSpawnerSettings);
            if (WeightedSelector)
            {
                // Load ethereal/wind meshes for Firmamento Zephyr using modern UE 5.6 APIs
                FString EtherealMeshPath = FString::Printf(TEXT("/Game/Auracron/Meshes/Ethereal/%s_Wind"), *StructureName);
                FSoftObjectPath EtherealMeshSoftPath(EtherealMeshPath);
                TSoftObjectPtr<UStaticMesh> EtherealMesh(EtherealMeshSoftPath);

                if (!EtherealMesh.IsNull())
                {
                    WeightedSelector->MeshEntries.Add(FPCGMeshSelectorWeightedEntry(EtherealMesh, 100)); // 100% weight for ethereal mesh
                }
                else
                {
                    // FALLBACK: Use default sphere for ethereal structures
                    FSoftObjectPath DefaultMeshSoftPath(TEXT("/Engine/BasicShapes/Sphere"));
                    TSoftObjectPtr<UStaticMesh> DefaultMesh(DefaultMeshSoftPath);
                    WeightedSelector->MeshEntries.Add(FPCGMeshSelectorWeightedEntry(DefaultMesh, 100));
                }

                WeightedSelector->bUseAttributeMaterialOverrides = true;
                MeshSpawnerSettings->MeshSelectorParameters = WeightedSelector;
            }

            MeshSpawnerSettings->bApplyMeshBoundsToPoints = true;
        }

        // STEP 5: CREATE ATTRIBUTE FILTER FOR WIND PROPERTIES - Modern UE 5.6 API
        UPCGAttributeFilteringSettings* AttributeFilterSettings = nullptr;
        UPCGNode* AttributeFilterNode = PCGGraph->AddNodeOfType<UPCGAttributeFilteringSettings>(AttributeFilterSettings);
        if (AttributeFilterNode && AttributeFilterSettings)
        {
            // Configure attribute filter using modern UE 5.6 API
            AttributeFilterSettings->Operator = EPCGAttributeFilterOperator::Greater;
            AttributeFilterSettings->TargetAttribute.SetAttributeName(TEXT("Density"));
            AttributeFilterSettings->bUseConstantThreshold = true;
            AttributeFilterSettings->AttributeTypes.FloatValue = 0.5f; // Medium threshold for ethereal structures
        }

        // STEP 6: CONNECT NODES IN PIPELINE - MODERN UE 5.6.1 API
        if (SurfaceSamplerNode && DensityFilterNode)
        {
            PCGGraph->AddEdge(SurfaceSamplerNode, TEXT("Out"), DensityFilterNode, TEXT("In"));
        }
        if (DensityFilterNode && AttributeFilterNode)
        {
            PCGGraph->AddEdge(DensityFilterNode, TEXT("Out"), AttributeFilterNode, TEXT("In"));
        }
        if (AttributeFilterNode && TransformNode)
        {
            PCGGraph->AddEdge(AttributeFilterNode, TEXT("Out"), TransformNode, TEXT("In"));
        }
        if (TransformNode && MeshSpawnerNode)
        {
            PCGGraph->AddEdge(TransformNode, TEXT("Out"), MeshSpawnerNode, TEXT("In"));
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: FULLY CONFIGURED PCG for Firmamento Zephyr - %s with %d nodes"),
               *StructureName, PCGGraph->GetNodes().Num());
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring PCG for Firmamento Zephyr"));
    }
}

void UUnrealMCPArchitectureCommands::ConfigurePCGForAbismoUmbral(UPCGGraph* PCGGraph, const FString& StructureName)
{
    if (!PCGGraph || !IsValid(PCGGraph) || PCGGraph->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid PCG Graph for Abismo Umbral"));
        return;
    }

    // ROBUST CONFIGURATION FOR ABISMO UMBRAL (Shadow Abyss) - PRODUCTION READY
    try
    {
        // Set generation parameters for shadow/abyss structures using modern UE 5.6.1 API
        FString GraphName = FString::Printf(TEXT("AbismoUmbral_%s"), *StructureName);
        PCGGraph->Rename(*GraphName, nullptr, REN_DontCreateRedirectors | REN_ForceNoResetLoaders);

        // STEP 1: CREATE SURFACE SAMPLER NODE FOR ABYSSAL TERRAIN
        UPCGSurfaceSamplerSettings* SurfaceSamplerSettings = nullptr;
        UPCGNode* SurfaceSamplerNode = PCGGraph->AddNodeOfType<UPCGSurfaceSamplerSettings>(SurfaceSamplerSettings);
        if (SurfaceSamplerNode && SurfaceSamplerSettings)
        {
            SurfaceSamplerSettings->PointsPerSquaredMeter = 3.0f; // Dense sampling for twisted structures
            SurfaceSamplerSettings->PointExtents = FVector(40.0f, 40.0f, 80.0f); // Compact, twisted structures
            SurfaceSamplerSettings->bUnbounded = false;
        }

        // STEP 2: CREATE DENSITY FILTER FOR SHADOW STRUCTURES DISTRIBUTION
        UPCGDensityFilterSettings* DensityFilterSettings = nullptr;
        UPCGNode* DensityFilterNode = PCGGraph->AddNodeOfType<UPCGDensityFilterSettings>(DensityFilterSettings);
        if (DensityFilterNode && DensityFilterSettings)
        {
            DensityFilterSettings->LowerBound = 0.6f; // High density for corrupted realm
            DensityFilterSettings->UpperBound = 1.0f;
            DensityFilterSettings->bInvertFilter = false;
        }

        // STEP 3: CREATE TRANSFORM POINTS FOR TWISTED POSITIONING
        UPCGTransformPointsSettings* TransformSettings = nullptr;
        UPCGNode* TransformNode = PCGGraph->AddNodeOfType<UPCGTransformPointsSettings>(TransformSettings);
        if (TransformNode && TransformSettings)
        {
            TransformSettings->bApplyToAttribute = false;
            TransformSettings->OffsetMin = FVector(-50.0f, -50.0f, -100.0f); // Underground/sunken structures
            TransformSettings->OffsetMax = FVector(50.0f, 50.0f, 50.0f);
            TransformSettings->RotationMin = FRotator(-30.0f, -30.0f, 0.0f); // Twisted, corrupted angles
            TransformSettings->RotationMax = FRotator(30.0f, 30.0f, 360.0f);
            TransformSettings->ScaleMin = FVector(0.7f, 0.7f, 0.8f); // Smaller, more compact structures
            TransformSettings->ScaleMax = FVector(1.3f, 1.3f, 1.8f);
        }

        // STEP 4: CREATE STATIC MESH SPAWNER FOR SHADOW ARCHITECTURE - Modern UE 5.6 API
        UPCGStaticMeshSpawnerSettings* MeshSpawnerSettings = nullptr;
        UPCGNode* MeshSpawnerNode = PCGGraph->AddNodeOfType<UPCGStaticMeshSpawnerSettings>(MeshSpawnerSettings);
        if (MeshSpawnerNode && MeshSpawnerSettings)
        {
            // Set mesh selector type to weighted
            MeshSpawnerSettings->MeshSelectorType = UPCGMeshSelectorWeighted::StaticClass();

            // Create weighted mesh selector
            UPCGMeshSelectorWeighted* WeightedSelector = NewObject<UPCGMeshSelectorWeighted>(MeshSpawnerSettings);
            if (WeightedSelector)
            {
                // Load shadow/corrupted meshes for Abismo Umbral using modern UE 5.6 APIs
                FString ShadowMeshPath = FString::Printf(TEXT("/Game/Auracron/Meshes/Shadow/%s_Corrupted"), *StructureName);
                FSoftObjectPath ShadowMeshSoftPath(ShadowMeshPath);
                TSoftObjectPtr<UStaticMesh> ShadowMesh(ShadowMeshSoftPath);

                if (!ShadowMesh.IsNull())
                {
                    WeightedSelector->MeshEntries.Add(FPCGMeshSelectorWeightedEntry(ShadowMesh, 100)); // 100% weight for shadow mesh
                }
                else
                {
                    // FALLBACK: Use default cylinder for twisted structures
                    FSoftObjectPath DefaultMeshSoftPath(TEXT("/Engine/BasicShapes/Cylinder"));
                    TSoftObjectPtr<UStaticMesh> DefaultMesh(DefaultMeshSoftPath);
                    WeightedSelector->MeshEntries.Add(FPCGMeshSelectorWeightedEntry(DefaultMesh, 100));
                }

                MeshSpawnerSettings->MeshSelectorParameters = WeightedSelector;
            }

            MeshSpawnerSettings->bApplyMeshBoundsToPoints = true;
        }

        // STEP 5: CREATE ATTRIBUTE FILTER FOR CORRUPTION PROPERTIES - Modern UE 5.6 API
        UPCGAttributeFilteringSettings* AttributeFilterSettings = nullptr;
        UPCGNode* AttributeFilterNode = PCGGraph->AddNodeOfType<UPCGAttributeFilteringSettings>(AttributeFilterSettings);
        if (AttributeFilterNode && AttributeFilterSettings)
        {
            // Configure attribute filter using modern UE 5.6 API
            AttributeFilterSettings->Operator = EPCGAttributeFilterOperator::Greater;
            AttributeFilterSettings->TargetAttribute.SetAttributeName(TEXT("Density"));
            AttributeFilterSettings->bUseConstantThreshold = true;
            AttributeFilterSettings->AttributeTypes.FloatValue = 0.6f; // High threshold for corrupted structures
        }

        // STEP 6: CONNECT NODES IN PIPELINE - MODERN UE 5.6.1 API
        if (SurfaceSamplerNode && DensityFilterNode)
        {
            PCGGraph->AddEdge(SurfaceSamplerNode, TEXT("Out"), DensityFilterNode, TEXT("In"));
        }
        if (DensityFilterNode && AttributeFilterNode)
        {
            PCGGraph->AddEdge(DensityFilterNode, TEXT("Out"), AttributeFilterNode, TEXT("In"));
        }
        if (AttributeFilterNode && TransformNode)
        {
            PCGGraph->AddEdge(AttributeFilterNode, TEXT("Out"), TransformNode, TEXT("In"));
        }
        if (TransformNode && MeshSpawnerNode)
        {
            PCGGraph->AddEdge(TransformNode, TEXT("Out"), MeshSpawnerNode, TEXT("In"));
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: FULLY CONFIGURED PCG for Abismo Umbral - %s with %d nodes"),
               *StructureName, PCGGraph->GetNodes().Num());
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring PCG for Abismo Umbral"));
    }
}

void UUnrealMCPArchitectureCommands::ConfigurePCGDefault(UPCGGraph* PCGGraph, const FString& StructureName)
{
    if (!PCGGraph || !IsValid(PCGGraph) || PCGGraph->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid PCG Graph for default configuration"));
        return;
    }

    // ROBUST DEFAULT CONFIGURATION - PRODUCTION READY
    try
    {
        // Configure default PCG settings for unknown layers using modern UE 5.6.1 API
        FString GraphName = FString::Printf(TEXT("Default_%s"), *StructureName);
        PCGGraph->Rename(*GraphName, nullptr, REN_DontCreateRedirectors | REN_ForceNoResetLoaders);

        // STEP 1: CREATE SURFACE SAMPLER NODE FOR STANDARD TERRAIN
        UPCGSurfaceSamplerSettings* SurfaceSamplerSettings = nullptr;
        UPCGNode* SurfaceSamplerNode = PCGGraph->AddNodeOfType<UPCGSurfaceSamplerSettings>(SurfaceSamplerSettings);
        if (SurfaceSamplerNode && SurfaceSamplerSettings)
        {
            SurfaceSamplerSettings->PointsPerSquaredMeter = 1.0f; // Standard sampling
            SurfaceSamplerSettings->PointExtents = FVector(50.0f, 50.0f, 100.0f); // Standard structures
            SurfaceSamplerSettings->bUnbounded = false;
        }

        // STEP 2: CREATE DENSITY FILTER FOR STANDARD DISTRIBUTION
        UPCGDensityFilterSettings* DensityFilterSettings = nullptr;
        UPCGNode* DensityFilterNode = PCGGraph->AddNodeOfType<UPCGDensityFilterSettings>(DensityFilterSettings);
        if (DensityFilterNode && DensityFilterSettings)
        {
            DensityFilterSettings->LowerBound = 0.5f; // Medium density
            DensityFilterSettings->UpperBound = 1.0f;
            DensityFilterSettings->bInvertFilter = false;
        }

        // STEP 3: CREATE TRANSFORM POINTS FOR STANDARD POSITIONING
        UPCGTransformPointsSettings* TransformSettings = nullptr;
        UPCGNode* TransformNode = PCGGraph->AddNodeOfType<UPCGTransformPointsSettings>(TransformSettings);
        if (TransformNode && TransformSettings)
        {
            TransformSettings->bApplyToAttribute = false;
            TransformSettings->OffsetMin = FVector(0.0f, 0.0f, 0.0f);
            TransformSettings->OffsetMax = FVector(0.0f, 0.0f, 100.0f);
            TransformSettings->RotationMin = FRotator(0.0f, 0.0f, 0.0f);
            TransformSettings->RotationMax = FRotator(0.0f, 0.0f, 360.0f);
            TransformSettings->ScaleMin = FVector(1.0f, 1.0f, 1.0f); // Standard scale
            TransformSettings->ScaleMax = FVector(1.5f, 1.5f, 2.0f);
        }

        // STEP 4: CREATE STATIC MESH SPAWNER FOR STANDARD ARCHITECTURE - Modern UE 5.6 API
        UPCGStaticMeshSpawnerSettings* MeshSpawnerSettings = nullptr;
        UPCGNode* MeshSpawnerNode = PCGGraph->AddNodeOfType<UPCGStaticMeshSpawnerSettings>(MeshSpawnerSettings);
        if (MeshSpawnerNode && MeshSpawnerSettings)
        {
            // Set mesh selector type to weighted
            MeshSpawnerSettings->MeshSelectorType = UPCGMeshSelectorWeighted::StaticClass();

            // Create weighted mesh selector
            UPCGMeshSelectorWeighted* WeightedSelector = NewObject<UPCGMeshSelectorWeighted>(MeshSpawnerSettings);
            if (WeightedSelector)
            {
                // Use default cube for standard structures using modern UE 5.6 APIs
                FSoftObjectPath DefaultMeshSoftPath(TEXT("/Engine/BasicShapes/Cube"));
                TSoftObjectPtr<UStaticMesh> DefaultMesh(DefaultMeshSoftPath);
                WeightedSelector->MeshEntries.Add(FPCGMeshSelectorWeightedEntry(DefaultMesh, 100));

                MeshSpawnerSettings->MeshSelectorParameters = WeightedSelector;
            }

            MeshSpawnerSettings->bApplyMeshBoundsToPoints = true;
        }

        // STEP 5: CONNECT NODES IN PIPELINE - MODERN UE 5.6.1 API
        if (SurfaceSamplerNode && DensityFilterNode)
        {
            PCGGraph->AddEdge(SurfaceSamplerNode, TEXT("Out"), DensityFilterNode, TEXT("In"));
        }
        if (DensityFilterNode && TransformNode)
        {
            PCGGraph->AddEdge(DensityFilterNode, TEXT("Out"), TransformNode, TEXT("In"));
        }
        if (TransformNode && MeshSpawnerNode)
        {
            PCGGraph->AddEdge(TransformNode, TEXT("Out"), MeshSpawnerNode, TEXT("In"));
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: FULLY CONFIGURED default PCG for %s with %d nodes"),
               *StructureName, PCGGraph->GetNodes().Num());
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring default PCG"));
    }
}

FAuracronTowerConfig UUnrealMCPArchitectureCommands::GetLayerArchitecturalStyle(int32 LayerIndex)
{
    FAuracronTowerConfig Config;
    Config.LayerIndex = LayerIndex;

    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Golden/Organic architecture
            Config.TowerHeight = 500.0f;
            Config.TowerRadius = 100.0f;
            Config.TowerLevels = 3;
            Config.TowerScale = FVector(1.0f, 1.0f, 1.2f); // Taller structures
            Config.bUseHierarchicalInstancing = true;
            Config.bUsePCGGeneration = false; // More traditional architecture
            break;
        case 1: // Firmamento Zephyr - Crystalline/Ethereal architecture
            Config.TowerHeight = 800.0f;
            Config.TowerRadius = 80.0f;
            Config.TowerLevels = 5;
            Config.TowerScale = FVector(0.8f, 0.8f, 1.5f); // Thinner, taller structures
            Config.bUseHierarchicalInstancing = true;
            Config.bUsePCGGeneration = true; // More complex, ethereal designs
            break;
        case 2: // Abismo Umbral - Dark/Twisted architecture
            Config.TowerHeight = 600.0f;
            Config.TowerRadius = 120.0f;
            Config.TowerLevels = 4;
            Config.TowerScale = FVector(1.2f, 1.2f, 1.0f); // Wider, more imposing structures
            Config.bUseHierarchicalInstancing = true;
            Config.bUsePCGGeneration = true; // Complex, twisted designs
            break;
        default:
            Config.TowerHeight = 400.0f;
            Config.TowerRadius = 100.0f;
            Config.TowerLevels = 2;
            Config.TowerScale = FVector(1.0f, 1.0f, 1.0f);
            Config.bUseHierarchicalInstancing = false;
            Config.bUsePCGGeneration = false;
            break;
    }

    return Config;
}

// ========================================
// ROBUST UTILITY FUNCTIONS - UE 5.6.1
// ========================================

FString UUnrealMCPArchitectureCommands::GenerateUniqueActorName(UWorld* World, const FString& BaseName, int32 MaxAttempts)
{
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("GenerateUniqueActorName: Invalid world"));
        return FString();
    }

    if (BaseName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("GenerateUniqueActorName: Empty base name"));
        return FString();
    }

    FString UniqueName = BaseName;
    int32 NameCounter = 1;
    
    // Check if actor with this name already exists and generate unique name
    while (NameCounter <= MaxAttempts)
    {
        bool bNameExists = false;
        
        // Check all actors in the world for name conflicts
        for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AActor* ExistingActor = *ActorItr;
            if (ExistingActor && IsValid(ExistingActor))
            {
                FString ExistingName = ExistingActor->GetName();
                FString ExistingLabel = ExistingActor->GetActorLabel();
                
                // Check both internal name and display label
                if (ExistingName == UniqueName || ExistingLabel == UniqueName)
                {
                    bNameExists = true;
                    break;
                }
            }
        }
        
        if (!bNameExists)
        {
            // Found unique name
            if (NameCounter > 1)
            {
                UE_LOG(LogTemp, Log, TEXT("GenerateUniqueActorName: Generated unique name: %s (from %s, attempt %d)"), 
                       *UniqueName, *BaseName, NameCounter - 1);
            }
            return UniqueName;
        }
        
        // Generate new name with counter
        UniqueName = FString::Printf(TEXT("%s_%d"), *BaseName, NameCounter);
        NameCounter++;
    }
    
    // Could not generate unique name within max attempts
    UE_LOG(LogTemp, Error, TEXT("GenerateUniqueActorName: Could not generate unique name after %d attempts for: %s"), 
           MaxAttempts, *BaseName);
    return FString(); // Return empty string to indicate failure
}

// CORREÇÃO CRÍTICA: Implementação da função que cria Blueprints reais
UBlueprint* UUnrealMCPArchitectureCommands::CreateRealTowerBlueprint(const FAuracronTowerConfig& TowerConfig)
{
    // Headers are included at the top of the file

    // STEP 1: Create Blueprint package and asset
    FString BlueprintName = FString::Printf(TEXT("BP_%s"), *TowerConfig.TowerName);
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/MOBA/Towers/%s"), *BlueprintName);
    
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealTowerBlueprint: Failed to create package for %s"), *BlueprintName);
        return nullptr;
    }

    // STEP 2: Create Blueprint using modern UE 5.6.1 APIs
    UBlueprint* NewBlueprint = FKismetEditorUtilities::CreateBlueprint(
        AActor::StaticClass(),
        Package,
        FName(*BlueprintName),
        BPTYPE_Normal,
        UBlueprint::StaticClass(),
        UBlueprintGeneratedClass::StaticClass(),
        FName("CreateRealTowerBlueprint")
    );

    if (!NewBlueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealTowerBlueprint: Failed to create Blueprint for %s"), *BlueprintName);
        return nullptr;
    }

    // STEP 3: Add components to the Blueprint
    USimpleConstructionScript* SCS = NewBlueprint->SimpleConstructionScript;
    if (!SCS)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealTowerBlueprint: No construction script found for %s"), *BlueprintName);
        return nullptr;
    }

    // Add Root Scene Component
    USCS_Node* RootNode = SCS->CreateNode(USceneComponent::StaticClass(), TEXT("TowerRoot"));
    SCS->AddNode(RootNode);

    // Add Static Mesh Component for tower base
    USCS_Node* BaseMeshNode = SCS->CreateNode(UStaticMeshComponent::StaticClass(), TEXT("TowerBase"));
    SCS->AddNode(BaseMeshNode);
    BaseMeshNode->AttachToName = RootNode->GetVariableName();

    // Configure the static mesh component
    UStaticMeshComponent* BaseMeshTemplate = Cast<UStaticMeshComponent>(BaseMeshNode->ComponentTemplate);
    if (BaseMeshTemplate)
    {
        // Use appropriate mesh based on layer
        FString MeshPath;
        switch (TowerConfig.LayerIndex)
        {
            case 0: // Planície Radiante
                MeshPath = TEXT("/Engine/BasicShapes/Cylinder.Cylinder");
                break;
            case 1: // Firmamento Zephyr
                MeshPath = TEXT("/Engine/BasicShapes/Cone.Cone");
                break;
            case 2: // Abismo Umbral
                MeshPath = TEXT("/Engine/BasicShapes/Cube.Cube");
                break;
            default:
                MeshPath = TEXT("/Engine/BasicShapes/Cylinder.Cylinder");
                break;
        }

        UStaticMesh* TowerMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
        if (TowerMesh)
        {
            BaseMeshTemplate->SetStaticMesh(TowerMesh);
            
            // Set scale based on tower configuration
            FVector TowerScale = FVector(
                TowerConfig.TowerRadius / 50.0f,  // Base radius scaling
                TowerConfig.TowerRadius / 50.0f,
                TowerConfig.TowerHeight / 100.0f  // Height scaling
            );
            BaseMeshTemplate->SetRelativeScale3D(TowerScale);
        }
    }

    // Add collision component
    USCS_Node* CollisionNode = SCS->CreateNode(UBoxComponent::StaticClass(), TEXT("TowerCollision"));
    SCS->AddNode(CollisionNode);
    CollisionNode->AttachToName = RootNode->GetVariableName();

    // Configure collision
    UBoxComponent* CollisionTemplate = Cast<UBoxComponent>(CollisionNode->ComponentTemplate);
    if (CollisionTemplate)
    {
        CollisionTemplate->SetBoxExtent(FVector(TowerConfig.TowerRadius, TowerConfig.TowerRadius, TowerConfig.TowerHeight / 2.0f));
        CollisionTemplate->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        CollisionTemplate->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    }

    // STEP 4: Compile and save the Blueprint
    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);
    
    // Mark package as dirty and save
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewBlueprint);

    UE_LOG(LogTemp, Log, TEXT("CreateRealTowerBlueprint: Successfully created Blueprint %s at %s"), 
           *BlueprintName, *PackagePath);

    return NewBlueprint;
}

UBlueprint* UUnrealMCPArchitectureCommands::CreateRealMinionBlueprint(const FString& MinionName, const FString& MinionType, int32 LayerIndex)
{
    // STEP 1: Create Blueprint package and asset
    FString BlueprintName = FString::Printf(TEXT("BP_%s"), *MinionName);
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/MOBA/Minions/%s"), *BlueprintName);
    
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealMinionBlueprint: Failed to create package for %s"), *BlueprintName);
        return nullptr;
    }

    // STEP 2: Create Blueprint using Pawn as base class (for AI movement)
    UBlueprint* NewBlueprint = FKismetEditorUtilities::CreateBlueprint(
        APawn::StaticClass(),
        Package,
        FName(*BlueprintName),
        BPTYPE_Normal,
        UBlueprint::StaticClass(),
        UBlueprintGeneratedClass::StaticClass(),
        FName("CreateRealMinionBlueprint")
    );

    if (!NewBlueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealMinionBlueprint: Failed to create Blueprint for %s"), *BlueprintName);
        return nullptr;
    }

    // STEP 3: Add components to the Blueprint
    USimpleConstructionScript* SCS = NewBlueprint->SimpleConstructionScript;
    if (!SCS)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealMinionBlueprint: No construction script found for %s"), *BlueprintName);
        return nullptr;
    }

    // Add Root Scene Component
    USCS_Node* RootNode = SCS->CreateNode(USceneComponent::StaticClass(), TEXT("MinionRoot"));
    SCS->AddNode(RootNode);

    // Add Static Mesh Component for minion body
    USCS_Node* BodyMeshNode = SCS->CreateNode(UStaticMeshComponent::StaticClass(), TEXT("MinionBody"));
    SCS->AddNode(BodyMeshNode);
    BodyMeshNode->AttachToName = RootNode->GetVariableName();

    // Configure the static mesh component based on minion type
    UStaticMeshComponent* BodyMeshTemplate = Cast<UStaticMeshComponent>(BodyMeshNode->ComponentTemplate);
    if (BodyMeshTemplate)
    {
        FString MeshPath;
        FVector MinionScale;
        
        if (MinionType == TEXT("Luz"))
        {
            MeshPath = TEXT("/Engine/BasicShapes/Sphere.Sphere");
            MinionScale = FVector(0.8f, 0.8f, 0.8f);
        }
        else if (MinionType == TEXT("Vento"))
        {
            MeshPath = TEXT("/Engine/BasicShapes/Cone.Cone");
            MinionScale = FVector(0.6f, 0.6f, 1.2f);
        }
        else if (MinionType == TEXT("Sombra"))
        {
            MeshPath = TEXT("/Engine/BasicShapes/Cube.Cube");
            MinionScale = FVector(1.0f, 1.0f, 0.5f);
        }
        else
        {
            MeshPath = TEXT("/Engine/BasicShapes/Sphere.Sphere");
            MinionScale = FVector(1.0f, 1.0f, 1.0f);
        }

        UStaticMesh* MinionMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
        if (MinionMesh)
        {
            BodyMeshTemplate->SetStaticMesh(MinionMesh);
            BodyMeshTemplate->SetRelativeScale3D(MinionScale);
        }
    }

    // Add Movement Component for AI pathfinding
    USCS_Node* MovementNode = SCS->CreateNode(UFloatingPawnMovement::StaticClass(), TEXT("MinionMovement"));
    SCS->AddNode(MovementNode);

    // Configure movement component
    UFloatingPawnMovement* MovementTemplate = Cast<UFloatingPawnMovement>(MovementNode->ComponentTemplate);
    if (MovementTemplate)
    {
        // Set movement speed based on minion type
        float MovementSpeed = 300.0f; // Default
        if (MinionType == TEXT("Luz"))
        {
            MovementSpeed = 300.0f;
        }
        else if (MinionType == TEXT("Vento"))
        {
            MovementSpeed = 350.0f;
        }
        else if (MinionType == TEXT("Sombra"))
        {
            MovementSpeed = 250.0f;
        }
        
        MovementTemplate->MaxSpeed = MovementSpeed;
        MovementTemplate->Acceleration = MovementSpeed * 2.0f;
        MovementTemplate->Deceleration = MovementSpeed * 3.0f;
    }

    // Add collision component
    USCS_Node* CollisionNode = SCS->CreateNode(UCapsuleComponent::StaticClass(), TEXT("MinionCollision"));
    SCS->AddNode(CollisionNode);
    CollisionNode->AttachToName = RootNode->GetVariableName();

    // Configure collision
    UCapsuleComponent* CollisionTemplate = Cast<UCapsuleComponent>(CollisionNode->ComponentTemplate);
    if (CollisionTemplate)
    {
        CollisionTemplate->SetCapsuleSize(50.0f, 100.0f);
        CollisionTemplate->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        CollisionTemplate->SetCollisionObjectType(ECollisionChannel::ECC_Pawn);
    }

    // STEP 4: Compile and save the Blueprint
    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);
    
    // Mark package as dirty and save
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewBlueprint);

    UE_LOG(LogTemp, Log, TEXT("CreateRealMinionBlueprint: Successfully created Blueprint %s at %s"), 
           *BlueprintName, *PackagePath);

    return NewBlueprint;
}

// CORREÇÃO CRÍTICA: Implementação do sistema de minions que cria Blueprints reais
TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateMinionSpawningSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION
    if (!Params->HasField(TEXT("minion_system_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: minion_system_name"));
    }

    FString SystemName = Params->GetStringField(TEXT("minion_system_name"));
    
    // Parse minion types array
    const TArray<TSharedPtr<FJsonValue>>* MinionTypesArray;
    if (!Params->TryGetArrayField(TEXT("minion_types"), MinionTypesArray))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: minion_types"));
    }

    // STEP 2: CREATE REAL MINION BLUEPRINTS
    TArray<FString> CreatedBlueprints;
    int32 SuccessfulCreations = 0;

    for (const TSharedPtr<FJsonValue>& MinionValue : *MinionTypesArray)
    {
        const TSharedPtr<FJsonObject>& MinionObj = MinionValue->AsObject();
        if (!MinionObj.IsValid()) continue;

        FString MinionName = MinionObj->GetStringField(TEXT("name"));
        FString MinionType = MinionName.Replace(TEXT("Minion_"), TEXT("")); // Extract type (Luz, Vento, Sombra)
        FString LayerName = MinionObj->GetStringField(TEXT("layer"));
        
        // Determine layer index from layer name
        int32 LayerIndex = 0;
        if (LayerName.Contains(TEXT("Firmamento"))) LayerIndex = 1;
        else if (LayerName.Contains(TEXT("Abismo"))) LayerIndex = 2;

        // Create the real Blueprint
        UBlueprint* MinionBlueprint = CreateRealMinionBlueprint(MinionName, MinionType, LayerIndex);
        
        if (MinionBlueprint)
        {
            CreatedBlueprints.Add(FString::Printf(TEXT("BP_%s"), *MinionName));
            SuccessfulCreations++;
            
            UE_LOG(LogTemp, Log, TEXT("HandleCreateMinionSpawningSystem: Created minion Blueprint %s"), *MinionName);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("HandleCreateMinionSpawningSystem: Failed to create minion Blueprint %s"), *MinionName);
        }
    }

    // STEP 3: CREATE MINION SPAWNER BLUEPRINT
    UBlueprint* SpawnerBlueprint = CreateMinionSpawnerBlueprint(SystemName);
    if (SpawnerBlueprint)
    {
        CreatedBlueprints.Add(FString::Printf(TEXT("BP_%s_Spawner"), *SystemName));
        SuccessfulCreations++;
    }

    // STEP 4: CREATE RESPONSE WITH REAL ASSET INFORMATION
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_minion_spawning_system"));
    Response->SetStringField(TEXT("system_name"), SystemName);
    Response->SetNumberField(TEXT("blueprints_created"), SuccessfulCreations);
    Response->SetBoolField(TEXT("success"), SuccessfulCreations > 0);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add array of created Blueprint names
    TArray<TSharedPtr<FJsonValue>> BlueprintArray;
    for (const FString& BlueprintName : CreatedBlueprints)
    {
        BlueprintArray.Add(MakeShared<FJsonValueString>(BlueprintName));
    }
    Response->SetArrayField(TEXT("created_blueprints"), BlueprintArray);

    // Add asset paths for easy access
    TArray<TSharedPtr<FJsonValue>> AssetPathArray;
    for (const FString& BlueprintName : CreatedBlueprints)
    {
        FString AssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/Minions/%s"), *BlueprintName);
        AssetPathArray.Add(MakeShared<FJsonValueString>(AssetPath));
    }
    Response->SetArrayField(TEXT("asset_paths"), AssetPathArray);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateMinionSpawningSystem: Created %d real Blueprint assets for system %s"), 
           SuccessfulCreations, *SystemName);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPArchitectureCommands::HandleCreateMultilayerTowerSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION
    if (!Params->HasField(TEXT("tower_system_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: tower_system_name"));
    }

    FString SystemName = Params->GetStringField(TEXT("tower_system_name"));
    
    // Parse layer configurations
    const TArray<TSharedPtr<FJsonValue>>* LayerConfigsArray;
    if (!Params->TryGetArrayField(TEXT("layer_configurations"), LayerConfigsArray))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: layer_configurations"));
    }

    // STEP 2: CREATE REAL TOWER BLUEPRINTS FOR EACH LAYER
    TArray<FString> CreatedBlueprints;
    int32 SuccessfulCreations = 0;

    for (int32 LayerIndex = 0; LayerIndex < LayerConfigsArray->Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>& LayerObj = (*LayerConfigsArray)[LayerIndex]->AsObject();
        if (!LayerObj.IsValid()) continue;

        FString LayerName = LayerObj->GetStringField(TEXT("layer_name"));
        
        // Create different tower types for each layer
        TArray<FString> TowerTypes = {TEXT("Basic"), TEXT("Advanced"), TEXT("Nexus")};
        
        for (const FString& TowerType : TowerTypes)
        {
            FString TowerName = FString::Printf(TEXT("Tower_%s_%s"), *TowerType, *LayerName);
            
            // Configure tower based on type and layer
            FAuracronTowerConfig TowerConfig;
            TowerConfig.TowerName = TowerName;
            TowerConfig.LayerIndex = LayerIndex;
            TowerConfig.TeamIndex = 0; // Neutral for now
            
            if (TowerType == TEXT("Basic"))
            {
                TowerConfig.TowerHeight = 400.0f;
                TowerConfig.TowerRadius = 80.0f;
                TowerConfig.TowerLevels = 2;
            }
            else if (TowerType == TEXT("Advanced"))
            {
                TowerConfig.TowerHeight = 600.0f;
                TowerConfig.TowerRadius = 120.0f;
                TowerConfig.TowerLevels = 4;
            }
            else if (TowerType == TEXT("Nexus"))
            {
                TowerConfig.TowerHeight = 1000.0f;
                TowerConfig.TowerRadius = 200.0f;
                TowerConfig.TowerLevels = 6;
            }

            // Create the real Blueprint
            UBlueprint* TowerBlueprint = CreateRealTowerBlueprint(TowerConfig);
            
            if (TowerBlueprint)
            {
                CreatedBlueprints.Add(FString::Printf(TEXT("BP_%s"), *TowerName));
                SuccessfulCreations++;
                
                UE_LOG(LogTemp, Log, TEXT("HandleCreateMultilayerTowerSystem: Created tower Blueprint %s"), *TowerName);
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("HandleCreateMultilayerTowerSystem: Failed to create tower Blueprint %s"), *TowerName);
            }
        }
    }

    // STEP 3: CREATE TOWER MANAGER BLUEPRINT
    UBlueprint* ManagerBlueprint = CreateTowerManagerBlueprint(SystemName);
    if (ManagerBlueprint)
    {
        CreatedBlueprints.Add(FString::Printf(TEXT("BP_%s_Manager"), *SystemName));
        SuccessfulCreations++;
    }

    // STEP 4: CREATE RESPONSE WITH REAL ASSET INFORMATION
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_multilayer_tower_system"));
    Response->SetStringField(TEXT("system_name"), SystemName);
    Response->SetNumberField(TEXT("blueprints_created"), SuccessfulCreations);
    Response->SetBoolField(TEXT("success"), SuccessfulCreations > 0);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add array of created Blueprint names
    TArray<TSharedPtr<FJsonValue>> BlueprintArray;
    for (const FString& BlueprintName : CreatedBlueprints)
    {
        BlueprintArray.Add(MakeShared<FJsonValueString>(BlueprintName));
    }
    Response->SetArrayField(TEXT("created_blueprints"), BlueprintArray);

    // Add asset paths for easy access
    TArray<TSharedPtr<FJsonValue>> AssetPathArray;
    for (const FString& BlueprintName : CreatedBlueprints)
    {
        FString AssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/Towers/%s"), *BlueprintName);
        AssetPathArray.Add(MakeShared<FJsonValueString>(AssetPath));
    }
    Response->SetArrayField(TEXT("asset_paths"), AssetPathArray);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateMultilayerTowerSystem: Created %d real Blueprint assets for system %s"), 
           SuccessfulCreations, *SystemName);

    return Response;
}

// Helper function to create minion spawner Blueprint
UBlueprint* UUnrealMCPArchitectureCommands::CreateMinionSpawnerBlueprint(const FString& SystemName)
{
    FString BlueprintName = FString::Printf(TEXT("BP_%s_Spawner"), *SystemName);
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/MOBA/Spawners/%s"), *BlueprintName);
    
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package) return nullptr;

    UBlueprint* NewBlueprint = FKismetEditorUtilities::CreateBlueprint(
        AActor::StaticClass(),
        Package,
        FName(*BlueprintName),
        BPTYPE_Normal,
        UBlueprint::StaticClass(),
        UBlueprintGeneratedClass::StaticClass(),
        FName("CreateMinionSpawnerBlueprint")
    );

    if (!NewBlueprint) return nullptr;

    // Add components for spawning logic
    USimpleConstructionScript* SCS = NewBlueprint->SimpleConstructionScript;
    if (SCS)
    {
        // Add root component
        USCS_Node* RootNode = SCS->CreateNode(USceneComponent::StaticClass(), TEXT("SpawnerRoot"));
        SCS->AddNode(RootNode);

        // Add spawn point marker
        USCS_Node* MarkerNode = SCS->CreateNode(UStaticMeshComponent::StaticClass(), TEXT("SpawnMarker"));
        SCS->AddNode(MarkerNode);
        MarkerNode->AttachToName = RootNode->GetVariableName();

        // Configure marker mesh
        UStaticMeshComponent* MarkerTemplate = Cast<UStaticMeshComponent>(MarkerNode->ComponentTemplate);
        if (MarkerTemplate)
        {
            UStaticMesh* MarkerMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
            if (MarkerMesh)
            {
                MarkerTemplate->SetStaticMesh(MarkerMesh);
                MarkerTemplate->SetRelativeScale3D(FVector(0.5f, 0.5f, 0.5f));
            }
        }
    }

    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewBlueprint);

    return NewBlueprint;
}

// Helper function to create tower manager Blueprint
UBlueprint* UUnrealMCPArchitectureCommands::CreateTowerManagerBlueprint(const FString& SystemName)
{
    FString BlueprintName = FString::Printf(TEXT("BP_%s_Manager"), *SystemName);
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/MOBA/Managers/%s"), *BlueprintName);
    
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package) return nullptr;

    UBlueprint* NewBlueprint = FKismetEditorUtilities::CreateBlueprint(
        AActor::StaticClass(),
        Package,
        FName(*BlueprintName),
        BPTYPE_Normal,
        UBlueprint::StaticClass(),
        UBlueprintGeneratedClass::StaticClass(),
        FName("CreateTowerManagerBlueprint")
    );

    if (!NewBlueprint) return nullptr;

    FKismetEditorUtilities::CompileBlueprint(NewBlueprint);
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewBlueprint);

    return NewBlueprint;
}