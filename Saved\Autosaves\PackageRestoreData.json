{ 
 
 	 " R e s t o r e E n a b l e d " :   t r u e , 
 
 	 " P a c k a g e s " :   [ 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / A U R A C R O N " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " A U R A C R O N " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / A U R A C R O N _ A u t o 1 . u m a p " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 0 / M a t e r i a l _ P l a n i c i e _ R a d i a n t e " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M a t e r i a l _ P l a n i c i e _ R a d i a n t e " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 0 / M a t e r i a l _ P l a n i c i e _ R a d i a n t e _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 0 / I n s t a n c e s / M a t e r i a l _ P l a n i c i e _ R a d i a n t e _ I n s t a n c e " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M a t e r i a l _ P l a n i c i e _ R a d i a n t e _ I n s t a n c e " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 0 / I n s t a n c e s / M a t e r i a l _ P l a n i c i e _ R a d i a n t e _ I n s t a n c e _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 1 / M a t e r i a l _ F i r m a m e n t o _ Z e p h y r " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M a t e r i a l _ F i r m a m e n t o _ Z e p h y r " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 1 / M a t e r i a l _ F i r m a m e n t o _ Z e p h y r _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 1 / I n s t a n c e s / M a t e r i a l _ F i r m a m e n t o _ Z e p h y r _ I n s t a n c e " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M a t e r i a l _ F i r m a m e n t o _ Z e p h y r _ I n s t a n c e " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 1 / I n s t a n c e s / M a t e r i a l _ F i r m a m e n t o _ Z e p h y r _ I n s t a n c e _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 2 / M a t e r i a l _ A b i s m o _ U m b r a l " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M a t e r i a l _ A b i s m o _ U m b r a l " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 2 / M a t e r i a l _ A b i s m o _ U m b r a l _ A u t o 1 . u a s s e t " 
 
 	 	 } , 
 
 	 	 { 
 
 	 	 	 " P a c k a g e P a t h N a m e " :   " / G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 2 / I n s t a n c e s / M a t e r i a l _ A b i s m o _ U m b r a l _ I n s t a n c e " , 
 
 	 	 	 " P a c k a g e A s s e t N a m e " :   " M a t e r i a l _ A b i s m o _ U m b r a l _ I n s t a n c e " , 
 
 	 	 	 " A u t o S a v e P a t h " :   " G a m e / A u r a c r o n / M a t e r i a l s / L a y e r 2 / I n s t a n c e s / M a t e r i a l _ A b i s m o _ U m b r a l _ I n s t a n c e _ A u t o 1 . u a s s e t " 
 
 	 	 } 
 
 	 ] 
 
 } 