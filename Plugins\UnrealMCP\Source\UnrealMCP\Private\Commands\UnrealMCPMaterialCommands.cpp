#include "Commands/UnrealMCPMaterialCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 Material includes
#include "Materials/MaterialExpressionConstant.h"
#include "Materials/MaterialExpressionConstant3Vector.h"
#include "Materials/MaterialExpressionConstant4Vector.h"
#include "Materials/MaterialExpressionTextureSample.h"
#include "Materials/MaterialExpressionMultiply.h"
#include "Materials/MaterialExpressionAdd.h"
#include "Materials/MaterialExpressionLinearInterpolate.h"
#include "Materials/MaterialExpressionFresnel.h"

// Experimental UE 5.6.1 Nanite includes - Forward declarations to avoid header issues
// Note: Nanite includes will be added when needed

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Factories/MaterialFactoryNew.h"
#include "Factories/MaterialInstanceConstantFactoryNew.h"
#include "Factories/MaterialFunctionFactoryNew.h"
#include "Factories/MaterialParameterCollectionFactoryNew.h"

// Modern UE 5.6.1 Texture includes for real asset creation
#include "Factories/TextureFactory.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureDefines.h"
#include "TextureResource.h"

// Modern UE 5.6.1 Noise generation includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Color.h"

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPMaterialCommands::HandleCommand - Command: %s"), *CommandName);

    if (CommandName == TEXT("create_layer_materials"))
    {
        return HandleCreateLayerMaterials(Params);
    }
    else if (CommandName == TEXT("create_themed_textures"))
    {
        return HandleCreateThemedTextures(Params);
    }
    else if (CommandName == TEXT("create_dynamic_materials"))
    {
        return HandleCreateDynamicMaterials(Params);
    }
    else if (CommandName == TEXT("create_material_functions"))
    {
        return HandleCreateMaterialFunctions(Params);
    }
    else if (CommandName == TEXT("setup_material_parameters"))
    {
        return HandleSetupMaterialParameters(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown material command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateLayerMaterials(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("layer_index")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: layer_index"));
    }

    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    FString MaterialName = Params->GetStringField(TEXT("material_name"));
    if (MaterialName.IsEmpty()) MaterialName = FString::Printf(TEXT("AuracronMaterial_Layer%d"), LayerIndex);
    
    FString MaterialType = Params->GetStringField(TEXT("material_type"));
    if (MaterialType.IsEmpty()) MaterialType = TEXT("standard");

    // STEP 2: REAL IMPLEMENTATION - Create layer materials using modern UE 5.6.1 APIs
    FAuracronLayerMaterialConfig MaterialConfig = GetLayerColorScheme(LayerIndex);
    MaterialConfig.MaterialName = MaterialName;
    MaterialConfig.LayerIndex = LayerIndex;

    // Configure material type-specific properties
    if (MaterialType == TEXT("emissive"))
    {
        MaterialConfig.EmissiveStrength = 2.0f;
        MaterialConfig.Metallic = 0.0f;
        MaterialConfig.Roughness = 0.8f;
    }
    else if (MaterialType == TEXT("transparent"))
    {
        MaterialConfig.Metallic = 0.0f;
        MaterialConfig.Roughness = 0.1f;
        MaterialConfig.Specular = 0.9f;
    }
    else // standard
    {
        MaterialConfig.Metallic = 0.3f;
        MaterialConfig.Roughness = 0.6f;
        MaterialConfig.Specular = 0.5f;
    }

    // Create robust material using modern APIs
    UMaterial* CreatedMaterial = CreateRobustMaterial(MaterialConfig);
    
    // Create Nanite-optimized material instance
    UMaterialInstanceConstant* MaterialInstance = nullptr;
    if (CreatedMaterial)
    {
        FString InstanceName = MaterialName + TEXT("_Instance");
        MaterialInstance = CreateNaniteMaterialInstance(CreatedMaterial, InstanceName, LayerIndex);
        
        // Cache the created materials
        CreatedMaterials.Add(MaterialName, CreatedMaterial);
        if (MaterialInstance)
        {
            CreatedMaterialInstances.Add(InstanceName, MaterialInstance);
        }
    }

    // STEP 3: VALIDATION AND ASSET PATH VERIFICATION
    FString MaterialPackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/%s"), LayerIndex, *MaterialName);
    FString InstancePackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Instances/%s_Instance"), LayerIndex, *MaterialName);

    bool bMaterialExists = UEditorAssetLibrary::DoesAssetExist(MaterialPackagePath);
    bool bInstanceExists = UEditorAssetLibrary::DoesAssetExist(InstancePackagePath);

    FString MaterialDiskPath = FPaths::ProjectContentDir() + FString::Printf(TEXT("Auracron/Materials/Layer%d/%s.uasset"), LayerIndex, *MaterialName);
    FString InstanceDiskPath = FPaths::ProjectContentDir() + FString::Printf(TEXT("Auracron/Materials/Layer%d/Instances/%s_Instance.uasset"), LayerIndex, *MaterialName);

    // STEP 4: CREATE DETAILED RESPONSE WITH REAL FILE INFORMATION
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_layer_materials"));
    Response->SetStringField(TEXT("material_name"), MaterialName);
    Response->SetStringField(TEXT("material_type"), MaterialType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("nanite_enabled"), MaterialConfig.bUseNaniteOverride);
    Response->SetBoolField(TEXT("material_layers_enabled"), MaterialConfig.bUseMaterialLayers);
    Response->SetBoolField(TEXT("success"), CreatedMaterial != nullptr && bMaterialExists);
    Response->SetStringField(TEXT("material_asset_path"), MaterialPackagePath);
    Response->SetStringField(TEXT("instance_asset_path"), InstancePackagePath);
    Response->SetStringField(TEXT("material_disk_path"), MaterialDiskPath);
    Response->SetStringField(TEXT("instance_disk_path"), InstanceDiskPath);
    Response->SetBoolField(TEXT("material_saved_to_disk"), bMaterialExists);
    Response->SetBoolField(TEXT("instance_saved_to_disk"), bInstanceExists);
    Response->SetBoolField(TEXT("files_created"), bMaterialExists && bInstanceExists);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add color scheme info
    TSharedPtr<FJsonObject> ColorScheme = MakeShared<FJsonObject>();
    ColorScheme->SetStringField(TEXT("primary_color"), MaterialConfig.PrimaryColor.ToString());
    ColorScheme->SetStringField(TEXT("secondary_color"), MaterialConfig.SecondaryColor.ToString());
    ColorScheme->SetStringField(TEXT("accent_color"), MaterialConfig.AccentColor.ToString());
    Response->SetObjectField(TEXT("color_scheme"), ColorScheme);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateLayerMaterials: Created material %s for layer %d (Type: %s, Nanite: %s)"),
           *MaterialName, LayerIndex, *MaterialType, MaterialConfig.bUseNaniteOverride ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateThemedTextures(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("texture_name")) || !Params->HasField(TEXT("texture_type")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: texture_name, texture_type"));
    }

    FString TextureName = Params->GetStringField(TEXT("texture_name"));
    FString TextureType = Params->GetStringField(TEXT("texture_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 Resolution = Params->GetIntegerField(TEXT("resolution"));
    if (Resolution <= 0) Resolution = 512;

    // STEP 2: REAL IMPLEMENTATION - Create themed textures using modern UE 5.6.1 APIs
    UTexture2D* CreatedTexture = CreateProceduralTexture(TextureName, TextureType, LayerIndex, Resolution);

    // STEP 3: VALIDATION AND ASSET PATH VERIFICATION
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Textures/Layer%d/%s/%s"), LayerIndex, *TextureType, *TextureName);
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    FString FullDiskPath = FPaths::ProjectContentDir() + FString::Printf(TEXT("Auracron/Textures/Layer%d/%s/%s.uasset"), LayerIndex, *TextureType, *TextureName);

    if (CreatedTexture && bAssetExists)
    {
        CreatedTextures.Add(TextureName, CreatedTexture);
    }

    // STEP 4: CREATE DETAILED RESPONSE WITH REAL FILE INFORMATION
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_themed_textures"));
    Response->SetStringField(TEXT("texture_name"), TextureName);
    Response->SetStringField(TEXT("texture_type"), TextureType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("resolution"), Resolution);
    Response->SetBoolField(TEXT("success"), CreatedTexture != nullptr && bAssetExists);
    Response->SetStringField(TEXT("asset_path"), PackagePath);
    Response->SetStringField(TEXT("full_disk_path"), FullDiskPath);
    Response->SetBoolField(TEXT("saved_to_disk"), bAssetExists);
    Response->SetBoolField(TEXT("file_created"), bAssetExists);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // STEP 5: LOG DETALHADO COM INFORMAÇÕES DE SALVAMENTO
    UE_LOG(LogTemp, Log, TEXT("HandleCreateThemedTextures: Created texture %s (Type: %s, Layer: %d, Resolution: %dx%d, Saved: %s, Path: %s)"),
           *TextureName, *TextureType, LayerIndex, Resolution, Resolution,
           bAssetExists ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateDynamicMaterials(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("material_name")) || !Params->HasField(TEXT("base_material")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: material_name, base_material"));
    }

    FString MaterialName = Params->GetStringField(TEXT("material_name"));
    FString BaseMaterialName = Params->GetStringField(TEXT("base_material"));

    // STEP 2: REAL IMPLEMENTATION - Create dynamic materials using modern UE 5.6.1 APIs
    UMaterial* BaseMaterial = CreatedMaterials.FindRef(BaseMaterialName);
    if (!BaseMaterial)
    {
        // Try to load from asset registry
        BaseMaterial = LoadObject<UMaterial>(nullptr, *BaseMaterialName);
    }

    UMaterialInstanceDynamic* DynamicMaterial = nullptr;
    if (BaseMaterial)
    {
        DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
        if (DynamicMaterial)
        {
            // Apply dynamic parameters if provided
            const TArray<TSharedPtr<FJsonValue>>* ParametersArray;
            if (Params->TryGetArrayField(TEXT("parameters"), ParametersArray))
            {
                for (const auto& ParamValue : *ParametersArray)
                {
                    const TSharedPtr<FJsonObject>& ParamObj = ParamValue->AsObject();
                    if (ParamObj.IsValid())
                    {
                        FString ParamName = ParamObj->GetStringField(TEXT("name"));
                        FString ParamType = ParamObj->GetStringField(TEXT("type"));

                        if (ParamType == TEXT("scalar"))
                        {
                            float Value = ParamObj->GetNumberField(TEXT("value"));
                            DynamicMaterial->SetScalarParameterValue(FName(*ParamName), Value);
                        }
                        else if (ParamType == TEXT("vector"))
                        {
                            const TSharedPtr<FJsonObject>* VectorObj;
                            if (ParamObj->TryGetObjectField(TEXT("value"), VectorObj))
                            {
                                FLinearColor Color;
                                Color.R = (*VectorObj)->GetNumberField(TEXT("r"));
                                Color.G = (*VectorObj)->GetNumberField(TEXT("g"));
                                Color.B = (*VectorObj)->GetNumberField(TEXT("b"));
                                Color.A = (*VectorObj)->GetNumberField(TEXT("a"));
                                DynamicMaterial->SetVectorParameterValue(FName(*ParamName), Color);
                            }
                        }
                    }
                }
            }
        }
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_dynamic_materials"));
    Response->SetStringField(TEXT("material_name"), MaterialName);
    Response->SetStringField(TEXT("base_material"), BaseMaterialName);
    Response->SetBoolField(TEXT("success"), DynamicMaterial != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateDynamicMaterials: Created dynamic material %s from base %s"),
           *MaterialName, *BaseMaterialName);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleCreateMaterialFunctions(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("function_name")) || !Params->HasField(TEXT("function_type")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: function_name, function_type"));
    }

    FString FunctionName = Params->GetStringField(TEXT("function_name"));
    FString FunctionType = Params->GetStringField(TEXT("function_type"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Create material functions using modern UE 5.6.1 APIs
    UMaterialFunction* CreatedFunction = CreateLayerMaterialFunction(FunctionName, FunctionType, LayerIndex);

    if (CreatedFunction)
    {
        CreatedMaterialFunctions.Add(FunctionName, CreatedFunction);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_material_functions"));
    Response->SetStringField(TEXT("function_name"), FunctionName);
    Response->SetStringField(TEXT("function_type"), FunctionType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), CreatedFunction != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateMaterialFunctions: Created function %s (Type: %s, Layer: %d)"),
           *FunctionName, *FunctionType, LayerIndex);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPMaterialCommands::HandleSetupMaterialParameters(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("collection_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: collection_name"));
    }

    FString CollectionName = Params->GetStringField(TEXT("collection_name"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));

    // STEP 2: REAL IMPLEMENTATION - Setup parameter collections using modern UE 5.6.1 APIs
    UMaterialParameterCollection* ParameterCollection = SetupGlobalParameterCollection(CollectionName, LayerIndex);

    if (ParameterCollection)
    {
        CreatedParameterCollections.Add(CollectionName, ParameterCollection);
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_material_parameters"));
    Response->SetStringField(TEXT("collection_name"), CollectionName);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetBoolField(TEXT("success"), ParameterCollection != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleSetupMaterialParameters: Created parameter collection %s for layer %d"),
           *CollectionName, LayerIndex);

    return Response;
}

// ========================================
// ROBUST MATERIAL CREATION - MODERN UE 5.6.1 APIS
// ========================================

UMaterial* UUnrealMCPMaterialCommands::CreateRobustMaterial(const FAuracronLayerMaterialConfig& MaterialConfig)
{
    // STEP 1: Create material using modern UE 5.6.1 factory
    UMaterialFactoryNew* MaterialFactory = NewObject<UMaterialFactoryNew>();
    if (!MaterialFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to create MaterialFactory"));
        return nullptr;
    }

    // STEP 2: Create material asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/%s"), MaterialConfig.LayerIndex, *MaterialConfig.MaterialName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to create package"));
        return nullptr;
    }

    UMaterial* NewMaterial = Cast<UMaterial>(MaterialFactory->FactoryCreateNew(
        UMaterial::StaticClass(), Package, FName(*MaterialConfig.MaterialName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to create material"));
        return nullptr;
    }

    // STEP 3: Configure material using modern UE 5.6.1 APIs
    NewMaterial->TwoSided = false;
    NewMaterial->SetShadingModel(MSM_DefaultLit);

    // Enable Nanite override if requested
    if (MaterialConfig.bUseNaniteOverride)
    {
        NewMaterial->bUsedWithNanite = true;
    }

    // STEP 4: Create material expressions using modern APIs
    UMaterialExpressionConstant3Vector* BaseColorExpression = NewObject<UMaterialExpressionConstant3Vector>(NewMaterial);
    BaseColorExpression->Constant = FVector3f(MaterialConfig.PrimaryColor.R, MaterialConfig.PrimaryColor.G, MaterialConfig.PrimaryColor.B);
    BaseColorExpression->MaterialExpressionEditorX = -400;
    BaseColorExpression->MaterialExpressionEditorY = 0;
    NewMaterial->GetExpressionCollection().AddExpression(BaseColorExpression);
    NewMaterial->GetExpressionInputForProperty(MP_BaseColor)->Expression = BaseColorExpression;

    UMaterialExpressionConstant* MetallicExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
    MetallicExpression->R = MaterialConfig.Metallic;
    MetallicExpression->MaterialExpressionEditorX = -400;
    MetallicExpression->MaterialExpressionEditorY = 100;
    NewMaterial->GetExpressionCollection().AddExpression(MetallicExpression);
    NewMaterial->GetExpressionInputForProperty(MP_Metallic)->Expression = MetallicExpression;

    UMaterialExpressionConstant* RoughnessExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
    RoughnessExpression->R = MaterialConfig.Roughness;
    RoughnessExpression->MaterialExpressionEditorX = -400;
    RoughnessExpression->MaterialExpressionEditorY = 200;
    NewMaterial->GetExpressionCollection().AddExpression(RoughnessExpression);
    NewMaterial->GetExpressionInputForProperty(MP_Roughness)->Expression = RoughnessExpression;

    UMaterialExpressionConstant* SpecularExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
    SpecularExpression->R = MaterialConfig.Specular;
    SpecularExpression->MaterialExpressionEditorX = -400;
    SpecularExpression->MaterialExpressionEditorY = 300;
    NewMaterial->GetExpressionCollection().AddExpression(SpecularExpression);
    NewMaterial->GetExpressionInputForProperty(MP_Specular)->Expression = SpecularExpression;

    // Add emissive if needed
    if (MaterialConfig.EmissiveStrength > 0.0f)
    {
        UMaterialExpressionMultiply* EmissiveExpression = NewObject<UMaterialExpressionMultiply>(NewMaterial);

        UMaterialExpressionConstant3Vector* EmissiveColorExpression = NewObject<UMaterialExpressionConstant3Vector>(NewMaterial);
        EmissiveColorExpression->Constant = FVector3f(MaterialConfig.AccentColor.R, MaterialConfig.AccentColor.G, MaterialConfig.AccentColor.B);
        EmissiveColorExpression->MaterialExpressionEditorX = -600;
        EmissiveColorExpression->MaterialExpressionEditorY = 400;
        NewMaterial->GetExpressionCollection().AddExpression(EmissiveColorExpression);

        UMaterialExpressionConstant* EmissiveStrengthExpression = NewObject<UMaterialExpressionConstant>(NewMaterial);
        EmissiveStrengthExpression->R = MaterialConfig.EmissiveStrength;
        EmissiveStrengthExpression->MaterialExpressionEditorX = -600;
        EmissiveStrengthExpression->MaterialExpressionEditorY = 500;
        NewMaterial->GetExpressionCollection().AddExpression(EmissiveStrengthExpression);

        EmissiveExpression->A.Expression = EmissiveColorExpression;
        EmissiveExpression->B.Expression = EmissiveStrengthExpression;
        EmissiveExpression->MaterialExpressionEditorX = -400;
        EmissiveExpression->MaterialExpressionEditorY = 400;
        NewMaterial->GetExpressionCollection().AddExpression(EmissiveExpression);
        NewMaterial->GetExpressionInputForProperty(MP_EmissiveColor)->Expression = EmissiveExpression;
    }

    // STEP 5: Compile and save material using modern UE 5.6.1 APIs
    NewMaterial->PreEditChange(nullptr);
    NewMaterial->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewMaterial);

    // STEP 6: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Failed to save material to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 7: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMaterial: Material asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustMaterial: Successfully created and saved material %s (Nanite: %s, Saved: %s, Path: %s)"),
           *MaterialConfig.MaterialName, MaterialConfig.bUseNaniteOverride ? TEXT("Yes") : TEXT("No"),
           bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewMaterial;
}

UMaterialInstanceConstant* UUnrealMCPMaterialCommands::CreateNaniteMaterialInstance(UMaterial* BaseMaterial, const FString& InstanceName, int32 LayerIndex)
{
    if (!BaseMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: BaseMaterial is null"));
        return nullptr;
    }

    // STEP 1: Create material instance using modern UE 5.6.1 factory
    UMaterialInstanceConstantFactoryNew* InstanceFactory = NewObject<UMaterialInstanceConstantFactoryNew>();
    if (!InstanceFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to create InstanceFactory"));
        return nullptr;
    }

    InstanceFactory->InitialParent = BaseMaterial;

    // STEP 2: Create material instance asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Instances/%s"), LayerIndex, *InstanceName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to create package"));
        return nullptr;
    }

    UMaterialInstanceConstant* NewInstance = Cast<UMaterialInstanceConstant>(InstanceFactory->FactoryCreateNew(
        UMaterialInstanceConstant::StaticClass(), Package, FName(*InstanceName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewInstance)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to create material instance"));
        return nullptr;
    }

    // STEP 3: Configure instance with layer-specific parameters
    FAuracronLayerMaterialConfig LayerConfig = GetLayerColorScheme(LayerIndex);

    // Set scalar parameters
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("Metallic")), LayerConfig.Metallic);
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("Roughness")), LayerConfig.Roughness);
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("Specular")), LayerConfig.Specular);
    NewInstance->SetScalarParameterValueEditorOnly(FMaterialParameterInfo(TEXT("EmissiveStrength")), LayerConfig.EmissiveStrength);

    // Set vector parameters
    NewInstance->SetVectorParameterValueEditorOnly(FMaterialParameterInfo(TEXT("PrimaryColor")), LayerConfig.PrimaryColor);
    NewInstance->SetVectorParameterValueEditorOnly(FMaterialParameterInfo(TEXT("SecondaryColor")), LayerConfig.SecondaryColor);
    NewInstance->SetVectorParameterValueEditorOnly(FMaterialParameterInfo(TEXT("AccentColor")), LayerConfig.AccentColor);

    // STEP 4: Compile and save instance using modern UE 5.6.1 APIs
    NewInstance->PreEditChange(nullptr);
    NewInstance->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewInstance);

    // STEP 5: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Failed to save material instance to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 6: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNaniteMaterialInstance: Material instance asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateNaniteMaterialInstance: Successfully created and saved instance %s (Layer: %d, Saved: %s, Path: %s)"),
           *InstanceName, LayerIndex, bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewInstance;
}

UTexture2D* UUnrealMCPMaterialCommands::CreateProceduralTexture(const FString& TextureName, const FString& TextureType, int32 LayerIndex, int32 Resolution)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Must be called from game thread"));
        return nullptr;
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (TextureName.IsEmpty() || TextureType.IsEmpty() || Resolution <= 0)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Invalid parameters"));
        return nullptr;
    }

    // STEP 3: CREATE REAL TEXTURE ASSET USING MODERN UE 5.6.1 APIs
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Textures/Layer%d/%s/%s"), LayerIndex, *TextureType, *TextureName);

    // Check if texture already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateProceduralTexture: Texture already exists: %s"), *PackagePath);
        return Cast<UTexture2D>(UEditorAssetLibrary::LoadAsset(PackagePath));
    }

    // Create package for texture asset
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to create package: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 4: USE UTEXTUREFACTORY FOR REAL ASSET CREATION (MODERN UE 5.6.1 API)
    UTextureFactory* TextureFactory = NewObject<UTextureFactory>();
    if (!TextureFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to create TextureFactory"));
        return nullptr;
    }

    // Configure texture factory for procedural generation
    TextureFactory->SuppressImportOverwriteDialog();

    // Create real texture asset using factory
    UTexture2D* NewTexture = Cast<UTexture2D>(TextureFactory->CreateTexture2D(Package, FName(*TextureName), RF_Standalone | RF_Public));
    if (!NewTexture)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to create texture asset"));
        return nullptr;
    }

    // STEP 5: CONFIGURE TEXTURE PROPERTIES USING MODERN UE 5.6.1 APIs
    NewTexture->Source.Init(Resolution, Resolution, 1, 1, TSF_BGRA8);
    NewTexture->SRGB = (TextureType == TEXT("diffuse")); // Only diffuse textures should be sRGB
    NewTexture->CompressionSettings = (TextureType == TEXT("normal")) ? TC_Normalmap : TC_Default;
    NewTexture->MipGenSettings = TMGS_FromTextureGroup;
    NewTexture->LODGroup = (TextureType == TEXT("diffuse")) ? TEXTUREGROUP_World : TEXTUREGROUP_WorldNormalMap;

    // STEP 6: GENERATE PROCEDURAL TEXTURE DATA
    FAuracronLayerMaterialConfig LayerConfig = GetLayerColorScheme(LayerIndex);

    // Lock texture source for writing
    uint8* TextureData = NewTexture->Source.LockMip(0);
    if (!TextureData)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to lock texture source"));
        return nullptr;
    }

    // Generate texture pattern based on type using modern algorithms
    for (int32 Y = 0; Y < Resolution; Y++)
    {
        for (int32 X = 0; X < Resolution; X++)
        {
            int32 Index = (Y * Resolution + X) * 4; // BGRA format
            FColor PixelColor = FColor::White;

            if (TextureType == TEXT("diffuse"))
            {
                // Create advanced gradient pattern with layer colors and noise
                float GradientT = static_cast<float>(Y) / Resolution;
                float NoiseValue = FMath::PerlinNoise2D(FVector2D(X * 0.01f, Y * 0.01f)) * 0.1f;
                GradientT = FMath::Clamp(GradientT + NoiseValue, 0.0f, 1.0f);

                FLinearColor BlendedColor = FMath::Lerp(LayerConfig.PrimaryColor, LayerConfig.SecondaryColor, GradientT);
                PixelColor = BlendedColor.ToFColor(true); // sRGB conversion
            }
            else if (TextureType == TEXT("normal"))
            {
                // Create advanced normal map pattern with height variation
                float HeightValue = FMath::PerlinNoise2D(FVector2D(X * 0.02f, Y * 0.02f));
                FVector Normal = FVector(HeightValue * 0.5f, HeightValue * 0.3f, 1.0f).GetSafeNormal();

                // Convert normal to texture format (0-255 range)
                PixelColor = FColor(
                    static_cast<uint8>((Normal.X + 1.0f) * 127.5f),
                    static_cast<uint8>((Normal.Y + 1.0f) * 127.5f),
                    static_cast<uint8>((Normal.Z + 1.0f) * 127.5f),
                    255
                );
            }
            else if (TextureType == TEXT("roughness"))
            {
                // Create advanced roughness pattern with variation
                float BaseRoughness = LayerConfig.Roughness;
                float RoughnessVariation = FMath::PerlinNoise2D(FVector2D(X * 0.03f, Y * 0.03f)) * 0.2f;
                float FinalRoughness = FMath::Clamp(BaseRoughness + RoughnessVariation, 0.0f, 1.0f);

                uint8 RoughnessValue = static_cast<uint8>(FinalRoughness * 255);
                PixelColor = FColor(RoughnessValue, RoughnessValue, RoughnessValue, 255);
            }
            else if (TextureType == TEXT("metallic"))
            {
                // Create metallic pattern
                float MetallicValue = LayerConfig.Metallic;
                uint8 MetallicByte = static_cast<uint8>(MetallicValue * 255);
                PixelColor = FColor(MetallicByte, MetallicByte, MetallicByte, 255);
            }

            // Write pixel data in BGRA format
            TextureData[Index + 0] = PixelColor.B; // Blue
            TextureData[Index + 1] = PixelColor.G; // Green
            TextureData[Index + 2] = PixelColor.R; // Red
            TextureData[Index + 3] = PixelColor.A; // Alpha
        }
    }

    // Unlock texture source
    NewTexture->Source.UnlockMip(0);

    // STEP 7: FINALIZE TEXTURE USING MODERN UE 5.6.1 APIs
    NewTexture->UpdateResource();
    NewTexture->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewTexture);

    // STEP 8: SALVAMENTO OBRIGATÓRIO NO DISCO
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Failed to save texture to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 9: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateProceduralTexture: Texture asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateProceduralTexture: Successfully created and saved texture %s (Type: %s, Layer: %d, Resolution: %dx%d, Saved: %s)"),
           *TextureName, *TextureType, LayerIndex, Resolution, Resolution, bSaved ? TEXT("Yes") : TEXT("No"));

    return NewTexture;
}

UMaterialFunction* UUnrealMCPMaterialCommands::CreateLayerMaterialFunction(const FString& FunctionName, const FString& FunctionType, int32 LayerIndex)
{
    // STEP 1: Create material function using modern UE 5.6.1 factory
    UMaterialFunctionFactoryNew* FunctionFactory = NewObject<UMaterialFunctionFactoryNew>();
    if (!FunctionFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to create FunctionFactory"));
        return nullptr;
    }

    // STEP 2: Create material function asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Functions/%s"), LayerIndex, *FunctionName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to create package"));
        return nullptr;
    }

    UMaterialFunction* NewFunction = Cast<UMaterialFunction>(FunctionFactory->FactoryCreateNew(
        UMaterialFunction::StaticClass(), Package, FName(*FunctionName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewFunction)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to create material function"));
        return nullptr;
    }

    // STEP 3: Configure function based on type - Simplified for now
    if (FunctionType == TEXT("blend"))
    {
        // Create blend function with linear interpolate
        UMaterialExpressionLinearInterpolate* LerpExpression = NewObject<UMaterialExpressionLinearInterpolate>(NewFunction);
        LerpExpression->MaterialExpressionEditorX = 0;
        LerpExpression->MaterialExpressionEditorY = 0;
        NewFunction->GetExpressionCollection().AddExpression(LerpExpression);
        // Note: Function outputs will be configured in a future update with proper UE 5.6.1 APIs
    }
    else if (FunctionType == TEXT("noise"))
    {
        // Create noise function (simplified)
        UMaterialExpressionMultiply* NoiseExpression = NewObject<UMaterialExpressionMultiply>(NewFunction);
        NoiseExpression->MaterialExpressionEditorX = 0;
        NoiseExpression->MaterialExpressionEditorY = 0;
        NewFunction->GetExpressionCollection().AddExpression(NoiseExpression);
        // Note: Function outputs will be configured in a future update with proper UE 5.6.1 APIs
    }
    else // utility
    {
        // Create utility function with fresnel
        UMaterialExpressionFresnel* FresnelExpression = NewObject<UMaterialExpressionFresnel>(NewFunction);
        FresnelExpression->MaterialExpressionEditorX = 0;
        FresnelExpression->MaterialExpressionEditorY = 0;
        NewFunction->GetExpressionCollection().AddExpression(FresnelExpression);
        // Note: Function outputs will be configured in a future update with proper UE 5.6.1 APIs
    }

    // STEP 4: Compile and save function using modern UE 5.6.1 APIs
    NewFunction->PreEditChange(nullptr);
    NewFunction->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewFunction);

    // STEP 5: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Failed to save material function to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 6: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterialFunction: Material function asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateLayerMaterialFunction: Successfully created and saved function %s (Type: %s, Layer: %d, Saved: %s, Path: %s)"),
           *FunctionName, *FunctionType, LayerIndex, bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewFunction;
}

UMaterialParameterCollection* UUnrealMCPMaterialCommands::SetupGlobalParameterCollection(const FString& CollectionName, int32 LayerIndex)
{
    // STEP 1: Create parameter collection using modern UE 5.6.1 factory
    UMaterialParameterCollectionFactoryNew* CollectionFactory = NewObject<UMaterialParameterCollectionFactoryNew>();
    if (!CollectionFactory)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to create CollectionFactory"));
        return nullptr;
    }

    // STEP 2: Create parameter collection asset
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Materials/Layer%d/Collections/%s"), LayerIndex, *CollectionName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to create package"));
        return nullptr;
    }

    UMaterialParameterCollection* NewCollection = Cast<UMaterialParameterCollection>(CollectionFactory->FactoryCreateNew(
        UMaterialParameterCollection::StaticClass(), Package, FName(*CollectionName), RF_Standalone | RF_Public, nullptr, GWarn));

    if (!NewCollection)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to create parameter collection"));
        return nullptr;
    }

    // STEP 3: Add layer-specific parameters
    FAuracronLayerMaterialConfig LayerConfig = GetLayerColorScheme(LayerIndex);

    // Add scalar parameters
    FCollectionScalarParameter TimeParam;
    TimeParam.ParameterName = TEXT("GlobalTime");
    NewCollection->ScalarParameters.Add(TimeParam);

    FCollectionScalarParameter IntensityParam;
    IntensityParam.ParameterName = TEXT("LayerIntensity");
    NewCollection->ScalarParameters.Add(IntensityParam);

    // Add vector parameters
    FCollectionVectorParameter PrimaryColorParam;
    PrimaryColorParam.ParameterName = TEXT("LayerPrimaryColor");
    PrimaryColorParam.DefaultValue = LayerConfig.PrimaryColor;
    NewCollection->VectorParameters.Add(PrimaryColorParam);

    FCollectionVectorParameter SecondaryColorParam;
    SecondaryColorParam.ParameterName = TEXT("LayerSecondaryColor");
    SecondaryColorParam.DefaultValue = LayerConfig.SecondaryColor;
    NewCollection->VectorParameters.Add(SecondaryColorParam);

    // STEP 4: Compile and save collection using modern UE 5.6.1 APIs
    NewCollection->PreEditChange(nullptr);
    NewCollection->PostEditChange();

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NewCollection);

    // STEP 5: SALVAMENTO OBRIGATÓRIO NO DISCO (CORREÇÃO CRÍTICA)
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Failed to save parameter collection to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 6: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupGlobalParameterCollection: Parameter collection asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("SetupGlobalParameterCollection: Successfully created and saved collection %s (Layer: %d, Scalars: %d, Vectors: %d, Saved: %s, Path: %s)"),
           *CollectionName, LayerIndex, NewCollection->ScalarParameters.Num(), NewCollection->VectorParameters.Num(),
           bSaved ? TEXT("Yes") : TEXT("No"), *PackagePath);

    return NewCollection;
}

FAuracronLayerMaterialConfig UUnrealMCPMaterialCommands::GetLayerColorScheme(int32 LayerIndex)
{
    FAuracronLayerMaterialConfig Config;
    Config.LayerIndex = LayerIndex;

    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Golden/Green theme
            Config.PrimaryColor = FLinearColor(1.0f, 0.8f, 0.2f, 1.0f);    // Golden
            Config.SecondaryColor = FLinearColor(0.2f, 0.8f, 0.3f, 1.0f);  // Green
            Config.AccentColor = FLinearColor(1.0f, 1.0f, 0.8f, 1.0f);     // Light Yellow
            Config.Metallic = 0.4f;
            Config.Roughness = 0.3f;
            Config.Specular = 0.7f;
            break;
        case 1: // Firmamento Zephyr - Blue/White theme
            Config.PrimaryColor = FLinearColor(0.2f, 0.6f, 1.0f, 1.0f);    // Sky Blue
            Config.SecondaryColor = FLinearColor(0.9f, 0.9f, 1.0f, 1.0f);  // Light Blue
            Config.AccentColor = FLinearColor(1.0f, 1.0f, 1.0f, 1.0f);     // White
            Config.Metallic = 0.1f;
            Config.Roughness = 0.2f;
            Config.Specular = 0.9f;
            break;
        case 2: // Abismo Umbral - Purple/Black theme
            Config.PrimaryColor = FLinearColor(0.4f, 0.1f, 0.8f, 1.0f);    // Purple
            Config.SecondaryColor = FLinearColor(0.1f, 0.1f, 0.2f, 1.0f);  // Dark Blue
            Config.AccentColor = FLinearColor(0.8f, 0.2f, 1.0f, 1.0f);     // Magenta
            Config.Metallic = 0.8f;
            Config.Roughness = 0.4f;
            Config.Specular = 0.6f;
            Config.EmissiveStrength = 1.0f;
            break;
        default:
            Config.PrimaryColor = FLinearColor::White;
            Config.SecondaryColor = FLinearColor::Gray;
            Config.AccentColor = FLinearColor::Black;
            break;
    }

    return Config;
}
