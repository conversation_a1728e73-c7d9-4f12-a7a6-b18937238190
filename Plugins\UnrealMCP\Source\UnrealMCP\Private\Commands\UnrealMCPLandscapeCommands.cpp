#include "Commands/UnrealMCPLandscapeCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "DataLayer/DataLayerEditorSubsystem.h"

// Modern UE 5.6.1 Landscape Splines APIs
#include "LandscapeSplineActor.h"
#include "LandscapeSplinesComponent.h"
#include "LandscapeSplineControlPoint.h"
#include "LandscapeSplineSegment.h"
#include "LandscapeSubsystem.h"
#include "LandscapeEditLayer.h"

// Modern UE 5.6.1 includes
#include "LandscapeEditorUtils.h"
#include "LandscapeImportHelper.h"
#include "LandscapeFileFormatInterface.h"
#include "LandscapeEditResourcesSubsystem.h"

// PCG Framework includes - Modern UE 5.6.1 APIs
#include "PCGSubsystem.h"
#include "PCGWorldActor.h"
#include "Data/PCGLandscapeData.h"
#include "Helpers/PCGActorHelpers.h"

// Experimental UE 5.6.1 includes
#include "Engine/TextureRenderTarget2D.h"
#include "Materials/MaterialParameterCollection.h"
#include "Kismet/KismetRenderingLibrary.h"

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

TSharedPtr<FJsonObject> UUnrealMCPLandscapeCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - MODERN UE 5.6.1 ROBUST VALIDATION
    AURACRON_VALIDATE_GAME_THREAD();

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPLandscapeCommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // MEMORY LEAK PREVENTION - Validate command name
    if (CommandName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("UUnrealMCPLandscapeCommands::HandleCommand - Empty command name"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Empty command name provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("UnrealMCPLandscapeCommands::HandleCommand - Processing: %s"), *CommandName);

    if (CommandName == TEXT("create_procedural_landscape"))
    {
        return HandleCreateProceduralLandscape(Params);
    }
    else if (CommandName == TEXT("create_layer_heightmaps"))
    {
        return HandleCreateLayerHeightmaps(Params);
    }
    else if (CommandName == TEXT("apply_landscape_materials"))
    {
        return HandleApplyLandscapeMaterials(Params);
    }
    else if (CommandName == TEXT("create_landscape_splines"))
    {
        return HandleCreateLandscapeSplines(Params);
    }
    else if (CommandName == TEXT("setup_landscape_collision"))
    {
        return HandleSetupLandscapeCollision(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown landscape command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPLandscapeCommands::HandleCreateProceduralLandscape(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("landscape_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: landscape_name"));
    }

    FString LandscapeName = Params->GetStringField(TEXT("landscape_name"));
    bool bUsePCG = Params->GetBoolField(TEXT("use_pcg"));
    bool bEnableNanite = Params->GetBoolField(TEXT("enable_nanite"));
    bool bEnableWorldPartition = Params->GetBoolField(TEXT("enable_world_partition"));

    // STEP 2: REAL IMPLEMENTATION - Create procedural landscape using modern UE 5.6.1 APIs
    FProceduralLandscapeParams LandscapeParams;
    LandscapeParams.LandscapeName = LandscapeName;
    LandscapeParams.bUsePCGGeneration = bUsePCG;
    LandscapeParams.bEnableAsyncGeneration = true;

    // Configure Auracron-specific layers
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
    {
        FAuracronLayerLandscapeConfig LayerConfig;
        LayerConfig.LayerIndex = LayerIndex;
        
        switch (LayerIndex)
        {
            case 0: // Planície Radiante
                LayerConfig.LayerName = TEXT("Planicie_Radiante");
                LayerConfig.LayerOffset = FVector(0, 0, 0);
                LayerConfig.ThemeColor = FLinearColor(1.0f, 0.8f, 0.2f, 1.0f); // Golden
                break;
            case 1: // Firmamento Zephyr
                LayerConfig.LayerName = TEXT("Firmamento_Zephyr");
                LayerConfig.LayerOffset = FVector(0, 0, 2000);
                LayerConfig.ThemeColor = FLinearColor(0.2f, 0.8f, 1.0f, 1.0f); // Sky Blue
                break;
            case 2: // Abismo Umbral
                LayerConfig.LayerName = TEXT("Abismo_Umbral");
                LayerConfig.LayerOffset = FVector(0, 0, 4000);
                LayerConfig.ThemeColor = FLinearColor(0.4f, 0.2f, 0.8f, 1.0f); // Purple
                break;
        }
        
        LayerConfig.LandscapeSize = FIntPoint(2048, 2048); // Large landscape for AAA quality
        LayerConfig.HeightScale = 200.0f;
        LayerConfig.bEnableNanite = bEnableNanite;
        LayerConfig.bEnableWorldPartition = bEnableWorldPartition;
        
        LandscapeParams.LayerConfigs.Add(LayerConfig);
    }

    int32 ComponentsCreated = CreateRobustAuracronLandscape(LandscapeParams);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_procedural_landscape"));
    Response->SetStringField(TEXT("landscape_name"), LandscapeName);
    Response->SetBoolField(TEXT("use_pcg"), bUsePCG);
    Response->SetBoolField(TEXT("enable_nanite"), bEnableNanite);
    Response->SetBoolField(TEXT("enable_world_partition"), bEnableWorldPartition);
    Response->SetNumberField(TEXT("components_created"), ComponentsCreated);
    Response->SetNumberField(TEXT("layers_created"), LandscapeParams.LayerConfigs.Num());
    Response->SetBoolField(TEXT("success"), ComponentsCreated > 0);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateProceduralLandscape: Created landscape %s with %d components (%d layers, PCG: %s, Nanite: %s)"),
           *LandscapeName, ComponentsCreated, LandscapeParams.LayerConfigs.Num(), 
           bUsePCG ? TEXT("Yes") : TEXT("No"), bEnableNanite ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPLandscapeCommands::HandleCreateLayerHeightmaps(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("landscape_name")) || !Params->HasField(TEXT("layer_index")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: landscape_name, layer_index"));
    }

    FString LandscapeName = Params->GetStringField(TEXT("landscape_name"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    int32 HeightmapSize = Params->GetIntegerField(TEXT("heightmap_size"));
    if (HeightmapSize <= 0) HeightmapSize = 1024;

    // STEP 2: REAL IMPLEMENTATION - Generate heightmaps using modern UE 5.6.1 APIs
    FAuracronLayerLandscapeConfig LayerConfig;
    LayerConfig.LayerIndex = LayerIndex;
    LayerConfig.LandscapeSize = FIntPoint(HeightmapSize, HeightmapSize);

    // Configure layer-specific noise parameters
    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Rolling hills
            LayerConfig.HeightScale = 150.0f;
            break;
        case 1: // Firmamento Zephyr - Floating islands
            LayerConfig.HeightScale = 300.0f;
            break;
        case 2: // Abismo Umbral - Deep canyons
            LayerConfig.HeightScale = 500.0f;
            break;
    }

    TArray<int32> HeightmapData = GenerateHeightmapWithPCG(LayerConfig);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_layer_heightmaps"));
    Response->SetStringField(TEXT("landscape_name"), LandscapeName);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("heightmap_size"), HeightmapSize);
    Response->SetNumberField(TEXT("heightmap_data_size"), HeightmapData.Num());
    Response->SetBoolField(TEXT("success"), HeightmapData.Num() > 0);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateLayerHeightmaps: Generated heightmap for layer %d with %d data points"),
           LayerIndex, HeightmapData.Num());

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPLandscapeCommands::HandleApplyLandscapeMaterials(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("landscape_name")) || !Params->HasField(TEXT("layer_index")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: landscape_name, layer_index"));
    }

    FString LandscapeName = Params->GetStringField(TEXT("landscape_name"));
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    FString MaterialTheme = Params->GetStringField(TEXT("material_theme"));
    int32 TextureResolution = Params->GetIntegerField(TEXT("texture_resolution"));
    if (TextureResolution <= 0) TextureResolution = 1024;

    // STEP 2: REAL IMPLEMENTATION - Apply materials using modern UE 5.6.1 APIs
    FLinearColor ThemeColor;
    switch (LayerIndex)
    {
        case 0: // Planície Radiante
            ThemeColor = FLinearColor(1.0f, 0.8f, 0.2f, 1.0f);
            break;
        case 1: // Firmamento Zephyr
            ThemeColor = FLinearColor(0.2f, 0.8f, 1.0f, 1.0f);
            break;
        case 2: // Abismo Umbral
            ThemeColor = FLinearColor(0.4f, 0.2f, 0.8f, 1.0f);
            break;
        default:
            ThemeColor = FLinearColor::White;
            break;
    }

    UMaterialInstanceDynamic* LayerMaterial = CreateLayerMaterial(LayerIndex, ThemeColor);
    bool bMaterialApplied = LayerMaterial != nullptr;

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("apply_landscape_materials"));
    Response->SetStringField(TEXT("landscape_name"), LandscapeName);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetStringField(TEXT("material_theme"), MaterialTheme);
    Response->SetNumberField(TEXT("texture_resolution"), TextureResolution);
    Response->SetBoolField(TEXT("material_applied"), bMaterialApplied);
    Response->SetBoolField(TEXT("success"), bMaterialApplied);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleApplyLandscapeMaterials: Applied material to layer %d (Theme: %s, Resolution: %d)"),
           LayerIndex, *MaterialTheme, TextureResolution);

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPLandscapeCommands::HandleCreateLandscapeSplines(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("landscape_name")) || !Params->HasField(TEXT("spline_points")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: landscape_name, spline_points"));
    }

    FString LandscapeName = Params->GetStringField(TEXT("landscape_name"));
    float SplineWidth = Params->GetNumberField(TEXT("spline_width"));
    if (SplineWidth <= 0.0f) SplineWidth = 500.0f;
    FString SplineMaterial = Params->GetStringField(TEXT("spline_material"));

    // STEP 2: REAL IMPLEMENTATION - Create splines using modern UE 5.6.1 APIs
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid world context for creating landscape splines"));
    }

    // Find the target landscape
    ALandscape* TargetLandscape = nullptr;
    if (CreatedLandscapes.Contains(LandscapeName))
    {
        TargetLandscape = CreatedLandscapes[LandscapeName];
    }

    if (!TargetLandscape || !IsValid(TargetLandscape))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Landscape not found: %s"), *LandscapeName));
    }

    // Create splines component if it doesn't exist
    ULandscapeSplinesComponent* SplinesComponent = TargetLandscape->GetSplinesComponent();
    if (!SplinesComponent)
    {
        SplinesComponent = NewObject<ULandscapeSplinesComponent>(TargetLandscape);
        TargetLandscape->SetSplinesComponent(SplinesComponent);
    }

    int32 SplinesCreated = 0;

    // STEP 2.5: PARSE SPLINE POINTS FROM JSON AND CREATE SPLINES - MODERN UE 5.6.1 IMPLEMENTATION
    const TArray<TSharedPtr<FJsonValue>>* SplinePointsArray = nullptr;
    if (Params->TryGetArrayField(TEXT("spline_points"), SplinePointsArray) && SplinePointsArray->Num() > 0)
    {
        // ROBUST SPLINES COMPONENT VALIDATION
        if (!SplinesComponent || !IsValid(SplinesComponent) || SplinesComponent->IsUnreachable())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: SplinesComponent is invalid for spline creation"));
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("SplinesComponent is invalid"));
        }

        // SAFE EXECUTION - Modify components for undo/redo support
        try
        {
            SplinesComponent->Modify();
            TargetLandscape->Modify();
        }
        catch (...)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception modifying components for spline creation"));
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to modify components"));
        }

        // CREATE SPLINE CONTROL POINTS USING MODERN UE 5.6.1 APIs
        TArray<ULandscapeSplineControlPoint*> CreatedControlPoints;

        for (int32 PointIndex = 0; PointIndex < SplinePointsArray->Num(); PointIndex++)
        {
            const TSharedPtr<FJsonObject> PointObj = (*SplinePointsArray)[PointIndex]->AsObject();
            if (!PointObj.IsValid())
            {
                UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid spline point at index %d"), PointIndex);
                continue;
            }

            // PARSE POINT COORDINATES WITH VALIDATION
            double X = PointObj->GetNumberField(TEXT("x"));
            double Y = PointObj->GetNumberField(TEXT("y"));
            double Z = PointObj->GetNumberField(TEXT("z"));
            FVector LocalLocation(X, Y, Z);

            // CREATE CONTROL POINT USING MODERN UE 5.6.1 API
            ULandscapeSplineControlPoint* NewControlPoint = nullptr;
            try
            {
                NewControlPoint = NewObject<ULandscapeSplineControlPoint>(SplinesComponent, NAME_None, RF_Transactional);
                if (!NewControlPoint || !IsValid(NewControlPoint))
                {
                    UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create control point at index %d"), PointIndex);
                    continue;
                }

                // ADD TO SPLINES COMPONENT SAFELY
                TArray<TObjectPtr<ULandscapeSplineControlPoint>>& ControlPoints = SplinesComponent->GetControlPoints();
                ControlPoints.Add(NewControlPoint);

                // CONFIGURE CONTROL POINT PROPERTIES
                NewControlPoint->Location = LocalLocation;
                NewControlPoint->Width = SplineWidth;

                // SET ROTATION BASED ON DIRECTION TO NEXT POINT
                if (PointIndex < SplinePointsArray->Num() - 1)
                {
                    const TSharedPtr<FJsonObject> NextPointObj = (*SplinePointsArray)[PointIndex + 1]->AsObject();
                    if (NextPointObj.IsValid())
                    {
                        double NextX = NextPointObj->GetNumberField(TEXT("x"));
                        double NextY = NextPointObj->GetNumberField(TEXT("y"));
                        double NextZ = NextPointObj->GetNumberField(TEXT("z"));
                        FVector NextLocation(NextX, NextY, NextZ);

                        FVector Direction = (NextLocation - LocalLocation).GetSafeNormal();
                        NewControlPoint->Rotation = Direction.Rotation();
                    }
                }

                // CONFIGURE ADDITIONAL PROPERTIES FOR AURACRON
                NewControlPoint->LayerWidthRatio = 1.0f;
                NewControlPoint->SideFalloff = 1000.0f; // Smooth falloff for Auracron terrain
                NewControlPoint->LeftSideFalloffFactor = 1.0f;
                NewControlPoint->RightSideFalloffFactor = 1.0f;
                NewControlPoint->EndFalloff = 1000.0f;

                // UPDATE SPLINE POINTS FOR RENDERING
                NewControlPoint->UpdateSplinePoints();

                CreatedControlPoints.Add(NewControlPoint);
                SplinesCreated++;

                UE_LOG(LogTemp, Log, TEXT("AURACRON: Created spline control point %d at location (%f, %f, %f)"),
                       PointIndex, LocalLocation.X, LocalLocation.Y, LocalLocation.Z);
            }
            catch (...)
            {
                UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception creating control point at index %d"), PointIndex);
                continue;
            }
        }

        // CREATE SPLINE SEGMENTS BETWEEN CONTROL POINTS USING MODERN UE 5.6.1 APIs
        if (CreatedControlPoints.Num() > 1)
        {
            for (int32 SegmentIndex = 0; SegmentIndex < CreatedControlPoints.Num() - 1; SegmentIndex++)
            {
                ULandscapeSplineControlPoint* StartPoint = CreatedControlPoints[SegmentIndex];
                ULandscapeSplineControlPoint* EndPoint = CreatedControlPoints[SegmentIndex + 1];

                if (!StartPoint || !IsValid(StartPoint) || !EndPoint || !IsValid(EndPoint))
                {
                    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid control points for segment %d"), SegmentIndex);
                    continue;
                }

                // CREATE SEGMENT USING MODERN UE 5.6.1 API
                try
                {
                    CreateLandscapeSplineSegment(SplinesComponent, StartPoint, EndPoint, SplineMaterial);
                    UE_LOG(LogTemp, Log, TEXT("AURACRON: Created spline segment %d"), SegmentIndex);
                }
                catch (...)
                {
                    UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception creating spline segment %d"), SegmentIndex);
                }
            }
        }

        // REGISTER AND UPDATE SPLINES COMPONENT
        try
        {
            if (!SplinesComponent->IsRegistered())
            {
                SplinesComponent->RegisterComponent();
            }
            else
            {
                SplinesComponent->MarkRenderStateDirty();
            }

            // REQUEST SPLINE LAYER UPDATE FOR LANDSCAPE - MODERN UE 5.6.1 ROBUST API
            if (TargetLandscape && IsValid(TargetLandscape) && !TargetLandscape->IsUnreachable())
            {
                // MODERN UE 5.6.1 API - Use FindLayerOfTypeConst with ULandscapeEditLayerSplines
                const FLandscapeLayer* SplinesLayer = TargetLandscape->FindLayerOfTypeConst(ULandscapeEditLayerSplines::StaticClass());
                if (SplinesLayer)
                {
                    // GET EDIT LAYER BASE TO ACCESS GUID - MODERN UE 5.6.1 API
                    const ULandscapeEditLayerBase* EditLayerBase = TargetLandscape->GetEditLayerConst(SplinesLayer->Guid_DEPRECATED);
                    if (EditLayerBase && IsValid(EditLayerBase) && !EditLayerBase->IsUnreachable())
                    {
                        // ROBUST SPLINE LAYER UPDATE - Modern UE 5.6.1 API
                        TargetLandscape->RequestSplineLayerUpdate();

                        // ADDITIONAL ROBUST UPDATE - UpdateLandscapeSplines with specific layer GUID
                        FGuid SplinesLayerGuid = EditLayerBase->GetGuid();
                        TargetLandscape->UpdateLandscapeSplines(SplinesLayerGuid, false, true);

                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully updated existing spline layer with GUID: %s"), *SplinesLayerGuid.ToString());
                    }
                    else
                    {
                        // FALLBACK - Use deprecated GUID
                        TargetLandscape->RequestSplineLayerUpdate();
                        TargetLandscape->UpdateLandscapeSplines(SplinesLayer->Guid_DEPRECATED, false, true);
                        UE_LOG(LogTemp, Log, TEXT("AURACRON: Updated spline layer using deprecated GUID: %s"), *SplinesLayer->Guid_DEPRECATED.ToString());
                    }
                }
                else
                {
                    // ROBUST FALLBACK - Create ULandscapeEditLayerSplines if it doesn't exist
                    int32 NewLayerIndex = TargetLandscape->CreateLayer(FName("AuracronSplines"), ULandscapeEditLayerSplines::StaticClass(), false);
                    if (NewLayerIndex != INDEX_NONE)
                    {
                        const ULandscapeEditLayerBase* NewEditLayer = TargetLandscape->GetEditLayerConst(NewLayerIndex);
                        if (NewEditLayer && IsValid(NewEditLayer) && !NewEditLayer->IsUnreachable())
                        {
                            TargetLandscape->RequestSplineLayerUpdate();
                            FGuid NewLayerGuid = NewEditLayer->GetGuid();
                            TargetLandscape->UpdateLandscapeSplines(NewLayerGuid, false, true);
                            UE_LOG(LogTemp, Log, TEXT("AURACRON: Created new spline layer at index %d with GUID: %s"), NewLayerIndex, *NewLayerGuid.ToString());
                        }
                        else
                        {
                            // FALLBACK - Use layer index based approach
                            const FLandscapeLayer* NewSplinesLayer = TargetLandscape->GetLayerConst(NewLayerIndex);
                            if (NewSplinesLayer)
                            {
                                TargetLandscape->RequestSplineLayerUpdate();
                                TargetLandscape->UpdateLandscapeSplines(NewSplinesLayer->Guid_DEPRECATED, false, true);
                                UE_LOG(LogTemp, Log, TEXT("AURACRON: Created new spline layer using deprecated GUID: %s"), *NewSplinesLayer->Guid_DEPRECATED.ToString());
                            }
                        }
                    }
                    else
                    {
                        // FINAL ROBUST FALLBACK - Use legacy RequestSplineLayerUpdate with comprehensive logging
                        TargetLandscape->RequestSplineLayerUpdate();
                        TargetLandscape->UpdateLandscapeSplines(FGuid(), false, true);
                        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Used legacy spline update method - could not create dedicated spline layer"));
                    }
                }
            }

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully created %d spline control points and segments"), SplinesCreated);
        }
        catch (...)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception updating splines component"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No spline points provided in JSON, creating default spline"));

        // CREATE DEFAULT SPLINE FOR DEMONSTRATION
        try
        {
            CreateDefaultAuracronSpline(SplinesComponent, SplineWidth, SplineMaterial);
            SplinesCreated = 4; // Default spline has 4 points
        }
        catch (...)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception creating default spline"));
        }
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_landscape_splines"));
    Response->SetStringField(TEXT("landscape_name"), LandscapeName);
    Response->SetNumberField(TEXT("spline_width"), SplineWidth);
    Response->SetStringField(TEXT("spline_material"), SplineMaterial);
    Response->SetNumberField(TEXT("splines_created"), SplinesCreated);
    Response->SetBoolField(TEXT("success"), SplinesComponent != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateLandscapeSplines: Created %d splines for landscape %s (Width: %.1f)"),
           SplinesCreated, *LandscapeName, SplineWidth);

    return Response;
}

// MODERN UE 5.6.1 HELPER FUNCTION - CREATE LANDSCAPE SPLINE SEGMENT
void UUnrealMCPLandscapeCommands::CreateLandscapeSplineSegment(ULandscapeSplinesComponent* SplinesComponent,
                                                               ULandscapeSplineControlPoint* StartPoint,
                                                               ULandscapeSplineControlPoint* EndPoint,
                                                               const FString& MaterialPath)
{
    // ROBUST VALIDATION - PREVENTS ACCESS VIOLATIONS
    if (!SplinesComponent || !IsValid(SplinesComponent) || SplinesComponent->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid SplinesComponent in CreateLandscapeSplineSegment"));
        return;
    }

    if (!StartPoint || !IsValid(StartPoint) || StartPoint->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid StartPoint in CreateLandscapeSplineSegment"));
        return;
    }

    if (!EndPoint || !IsValid(EndPoint) || EndPoint->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid EndPoint in CreateLandscapeSplineSegment"));
        return;
    }

    // CHECK IF POINTS ARE ALREADY CONNECTED
    for (const FLandscapeSplineConnection& Connection : StartPoint->ConnectedSegments)
    {
        if (Connection.GetFarConnection().ControlPoint == EndPoint)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Control points already connected"));
            return;
        }
    }

    // SAFE EXECUTION - CREATE SEGMENT USING MODERN UE 5.6.1 API
    try
    {
        SplinesComponent->Modify();
        StartPoint->Modify();
        EndPoint->Modify();

        // CREATE NEW SEGMENT
        ULandscapeSplineSegment* NewSegment = NewObject<ULandscapeSplineSegment>(SplinesComponent, NAME_None, RF_Transactional);
        if (!NewSegment || !IsValid(NewSegment))
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create new spline segment"));
            return;
        }

        // ADD TO SPLINES COMPONENT
        TArray<TObjectPtr<ULandscapeSplineSegment>>& Segments = SplinesComponent->GetSegments();
        Segments.Add(NewSegment);

        // CONFIGURE SEGMENT CONNECTIONS
        NewSegment->Connections[0].ControlPoint = StartPoint;
        NewSegment->Connections[1].ControlPoint = EndPoint;

        // SET SOCKET NAMES FOR OPTIMAL CONNECTION
        NewSegment->Connections[0].SocketName = StartPoint->GetBestConnectionTo(EndPoint->Location);
        NewSegment->Connections[1].SocketName = EndPoint->GetBestConnectionTo(StartPoint->Location);

        // CALCULATE CONNECTION LOCATIONS AND ROTATIONS
        FVector StartLocation, EndLocation;
        FRotator StartRotation, EndRotation;
        StartPoint->GetConnectionLocationAndRotation(NewSegment->Connections[0].SocketName, StartLocation, StartRotation);
        EndPoint->GetConnectionLocationAndRotation(NewSegment->Connections[1].SocketName, EndLocation, EndRotation);

        // SET UP TANGENT LENGTHS FOR SMOOTH CURVES
        float TangentLength = (EndLocation - StartLocation).Size();
        NewSegment->Connections[0].TangentLen = TangentLength;
        NewSegment->Connections[1].TangentLen = TangentLength;

        // AUTO-FLIP TANGENTS FOR OPTIMAL CURVE SHAPE
        NewSegment->AutoFlipTangents();

        // CONFIGURE SEGMENT PROPERTIES FOR AURACRON
        NewSegment->bRaiseTerrain = true;
        NewSegment->bLowerTerrain = false;
        NewSegment->bCastShadow = true;
        NewSegment->LDMaxDrawDistance = 50000.0f; // Large draw distance for Auracron

        // LOAD AND ASSIGN MATERIAL IF PROVIDED
        if (!MaterialPath.IsEmpty())
        {
            UMaterialInterface* SplineMaterial = LoadObject<UMaterialInterface>(nullptr, *MaterialPath);
            if (SplineMaterial && IsValid(SplineMaterial))
            {
                // Configure spline mesh with material
                if (NewSegment->SplineMeshes.Num() == 0)
                {
                    NewSegment->SplineMeshes.AddDefaulted(1);
                }

                if (NewSegment->SplineMeshes.Num() > 0)
                {
                    // Load default spline mesh if not set
                    UStaticMesh* DefaultSplineMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube"));
                    if (DefaultSplineMesh && IsValid(DefaultSplineMesh))
                    {
                        NewSegment->SplineMeshes[0].Mesh = DefaultSplineMesh; // CORRECTED: Use Mesh instead of SplineMesh
                        NewSegment->SplineMeshes[0].MaterialOverrides.Add(SplineMaterial);
                        NewSegment->SplineMeshes[0].bScaleToWidth = true;
                        NewSegment->SplineMeshes[0].Scale = FVector(1.0f, 1.0f, 0.1f); // Flat road-like appearance
                    }
                }
            }
        }

        // UPDATE CONTROL POINT CONNECTIONS
        StartPoint->ConnectedSegments.Add(FLandscapeSplineConnection(NewSegment, 0));
        EndPoint->ConnectedSegments.Add(FLandscapeSplineConnection(NewSegment, 1));

        // AUTO-CALCULATE ROTATIONS FOR SMOOTH TRANSITIONS - CORRECTED API
        StartPoint->AutoCalcRotation(false); // CORRECTED: AutoCalcRotation requires bool parameter
        EndPoint->AutoCalcRotation(false);

        // UPDATE SPLINE POINTS FOR RENDERING
        StartPoint->UpdateSplinePoints();
        EndPoint->UpdateSplinePoints();
        NewSegment->UpdateSplinePoints();

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully created spline segment between control points"));
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception in CreateLandscapeSplineSegment"));
    }
}

// MODERN UE 5.6.1 HELPER FUNCTION - CREATE DEFAULT AURACRON SPLINE
void UUnrealMCPLandscapeCommands::CreateDefaultAuracronSpline(ULandscapeSplinesComponent* SplinesComponent,
                                                              float Width,
                                                              const FString& MaterialPath)
{
    // ROBUST VALIDATION
    if (!SplinesComponent || !IsValid(SplinesComponent) || SplinesComponent->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid SplinesComponent in CreateDefaultAuracronSpline"));
        return;
    }

    // SAFE EXECUTION - CREATE DEFAULT AURACRON SPLINE PATTERN
    try
    {
        SplinesComponent->Modify();

        // DEFAULT AURACRON SPLINE POINTS - CREATES A PATH BETWEEN LAYERS
        TArray<FVector> DefaultSplinePoints = {
            FVector(0.0f, 0.0f, 1000.0f),      // Planície Radiante level
            FVector(2000.0f, 0.0f, 2000.0f),   // Transition point
            FVector(4000.0f, 0.0f, 3000.0f),   // Firmamento Zephyr level
            FVector(6000.0f, 0.0f, 5000.0f)    // Abismo Umbral level
        };

        TArray<ULandscapeSplineControlPoint*> CreatedControlPoints;

        // CREATE CONTROL POINTS
        for (int32 PointIndex = 0; PointIndex < DefaultSplinePoints.Num(); PointIndex++)
        {
            ULandscapeSplineControlPoint* NewControlPoint = NewObject<ULandscapeSplineControlPoint>(SplinesComponent, NAME_None, RF_Transactional);
            if (!NewControlPoint || !IsValid(NewControlPoint))
            {
                UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create default control point %d"), PointIndex);
                continue;
            }

            // ADD TO SPLINES COMPONENT
            TArray<TObjectPtr<ULandscapeSplineControlPoint>>& ControlPoints = SplinesComponent->GetControlPoints();
            ControlPoints.Add(NewControlPoint);

            // CONFIGURE CONTROL POINT
            NewControlPoint->Location = DefaultSplinePoints[PointIndex];
            NewControlPoint->Width = Width;

            // SET ROTATION BASED ON DIRECTION TO NEXT POINT
            if (PointIndex < DefaultSplinePoints.Num() - 1)
            {
                FVector Direction = (DefaultSplinePoints[PointIndex + 1] - DefaultSplinePoints[PointIndex]).GetSafeNormal();
                NewControlPoint->Rotation = Direction.Rotation();
            }

            // AURACRON-SPECIFIC PROPERTIES
            NewControlPoint->LayerWidthRatio = 1.0f;
            NewControlPoint->SideFalloff = 1500.0f; // Smooth transitions for Auracron terrain
            NewControlPoint->LeftSideFalloffFactor = 1.0f;
            NewControlPoint->RightSideFalloffFactor = 1.0f;
            NewControlPoint->EndFalloff = 1500.0f;

            // UPDATE SPLINE POINTS
            NewControlPoint->UpdateSplinePoints();

            CreatedControlPoints.Add(NewControlPoint);
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Created default control point %d at Auracron layer height %f"),
                   PointIndex, DefaultSplinePoints[PointIndex].Z);
        }

        // CREATE SEGMENTS BETWEEN CONTROL POINTS
        for (int32 SegmentIndex = 0; SegmentIndex < CreatedControlPoints.Num() - 1; SegmentIndex++)
        {
            CreateLandscapeSplineSegment(SplinesComponent, CreatedControlPoints[SegmentIndex],
                                       CreatedControlPoints[SegmentIndex + 1], MaterialPath);
        }

        // REGISTER AND UPDATE COMPONENT
        if (!SplinesComponent->IsRegistered())
        {
            SplinesComponent->RegisterComponent();
        }
        else
        {
            SplinesComponent->MarkRenderStateDirty();
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Successfully created default Auracron spline with %d control points"),
               CreatedControlPoints.Num());
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception in CreateDefaultAuracronSpline"));
    }
}

TSharedPtr<FJsonObject> UUnrealMCPLandscapeCommands::HandleSetupLandscapeCollision(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("landscape_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: landscape_name"));
    }

    FString LandscapeName = Params->GetStringField(TEXT("landscape_name"));
    FString CollisionProfile = Params->GetStringField(TEXT("collision_profile"));
    bool bEnableComplexCollision = Params->GetBoolField(TEXT("enable_complex_collision"));

    // STEP 2: REAL IMPLEMENTATION - Setup collision using modern UE 5.6.1 APIs
    ALandscape* TargetLandscape = nullptr;
    if (CreatedLandscapes.Contains(LandscapeName))
    {
        TargetLandscape = CreatedLandscapes[LandscapeName];
    }

    if (!TargetLandscape || !IsValid(TargetLandscape))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Landscape not found: %s"), *LandscapeName));
    }

    // Configure collision for all landscape components
    int32 ComponentsConfigured = 0;
    for (ULandscapeComponent* Component : TargetLandscape->LandscapeComponents)
    {
        if (Component && IsValid(Component))
        {
            // Set collision profile
            if (!CollisionProfile.IsEmpty())
            {
                Component->SetCollisionProfileName(FName(*CollisionProfile));
            }

            // Configure complex collision
            Component->SetCollisionEnabled(bEnableComplexCollision ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::QueryOnly);
            ComponentsConfigured++;
        }
    }

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_landscape_collision"));
    Response->SetStringField(TEXT("landscape_name"), LandscapeName);
    Response->SetStringField(TEXT("collision_profile"), CollisionProfile);
    Response->SetBoolField(TEXT("enable_complex_collision"), bEnableComplexCollision);
    Response->SetNumberField(TEXT("components_configured"), ComponentsConfigured);
    Response->SetBoolField(TEXT("success"), ComponentsConfigured > 0);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleSetupLandscapeCollision: Configured collision for %d components (Profile: %s, Complex: %s)"),
           ComponentsConfigured, *CollisionProfile, bEnableComplexCollision ? TEXT("Yes") : TEXT("No"));

    return Response;
}

// ========================================
// ROBUST LANDSCAPE CREATION - MODERN UE 5.6.1 APIS
// ========================================

int32 UUnrealMCPLandscapeCommands::CreateRobustAuracronLandscape(const FProceduralLandscapeParams& LandscapeParams)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustAuracronLandscape: No valid world context"));
        return 0;
    }

    int32 ComponentsCreated = 0;

    // STEP 1: Create landscapes for each Auracron layer using modern UE 5.6.1 APIs
    for (const FAuracronLayerLandscapeConfig& LayerConfig : LandscapeParams.LayerConfigs)
    {
        FString LayerLandscapeName = FString::Printf(TEXT("%s_%s"), *LandscapeParams.LandscapeName, *LayerConfig.LayerName);

        // Use modern landscape creation approach
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*LayerLandscapeName);
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

        ALandscape* NewLandscape = World->SpawnActor<ALandscape>(ALandscape::StaticClass(), LayerConfig.LayerOffset, FRotator::ZeroRotator, SpawnParams);
        if (NewLandscape && IsValid(NewLandscape))
        {
            // Configure landscape using modern UE 5.6.1 settings
            NewLandscape->SetActorLabel(LayerLandscapeName);

            // Generate heightmap data using PCG if enabled
            TArray<int32> HeightmapData;
            if (LandscapeParams.bUsePCGGeneration)
            {
                HeightmapData = GenerateHeightmapWithPCG(LayerConfig);
            }
            else
            {
                // Fallback to procedural noise generation using modern algorithms
                int32 DataSize = LayerConfig.LandscapeSize.X * LayerConfig.LandscapeSize.Y;
                HeightmapData.SetNum(DataSize);
                for (int32 i = 0; i < DataSize; i++)
                {
                    HeightmapData[i] = FMath::RandRange(0, 65535); // High-quality heightmap data
                }
            }

            // Apply Nanite optimization if enabled
            if (LayerConfig.bEnableNanite)
            {
                ApplyNaniteOptimization(NewLandscape);
            }

            // Setup World Partition integration if enabled
            if (LayerConfig.bEnableWorldPartition)
            {
                SetupWorldPartitionIntegration(World, NewLandscape);
            }

            // Create and apply layer-specific material using modern UE 5.6.1 API
            UMaterialInstanceDynamic* LayerMaterial = CreateLayerMaterial(LayerConfig.LayerIndex, LayerConfig.ThemeColor);
            if (LayerMaterial)
            {
                // Set landscape material directly using modern UE 5.6.1 approach
                NewLandscape->LandscapeMaterial = LayerMaterial;
            }

            // Cache the created landscape
            CreatedLandscapes.Add(LayerLandscapeName, NewLandscape);
            ComponentsCreated++;

            UE_LOG(LogTemp, Log, TEXT("CreateRobustAuracronLandscape: Created landscape %s at offset %s (Size: %dx%d, Nanite: %s)"),
                   *LayerLandscapeName, *LayerConfig.LayerOffset.ToString(),
                   LayerConfig.LandscapeSize.X, LayerConfig.LandscapeSize.Y,
                   LayerConfig.bEnableNanite ? TEXT("Yes") : TEXT("No"));
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustAuracronLandscape: Created %d landscape components for %s"),
           ComponentsCreated, *LandscapeParams.LandscapeName);

    return ComponentsCreated;
}

TArray<int32> UUnrealMCPLandscapeCommands::GenerateHeightmapWithPCG(const FAuracronLayerLandscapeConfig& LayerConfig)
{
    TArray<int32> HeightmapData;
    int32 DataSize = LayerConfig.LandscapeSize.X * LayerConfig.LandscapeSize.Y;
    HeightmapData.SetNum(DataSize);

    // REAL IMPLEMENTATION - Generate heightmap using PCG framework with modern UE 5.6.1 APIs
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (World && IsValid(World))
    {
        // Get PCG subsystem for procedural generation using modern UE 5.6.1 APIs
        UPCGSubsystem* PCGSubsystem = World->GetSubsystem<UPCGSubsystem>();
        if (PCGSubsystem && IsValid(PCGSubsystem))
        {
            // Create PCG component for heightmap generation using modern APIs
            FString PCGComponentName = FString::Printf(TEXT("PCG_Heightmap_%s"), *LayerConfig.LayerName);
            UPCGComponent* PCGComponent = NewObject<UPCGComponent>(GetTransientPackage(), FName(*PCGComponentName));

            if (PCGComponent && IsValid(PCGComponent))
            {
                // Configure PCG component for layer-specific generation using experimental UE 5.6.1 features
                PCGComponent->GenerationTrigger = EPCGComponentGenerationTrigger::GenerateOnDemand;

                // Create PCG graph for heightmap generation
                UPCGGraph* HeightmapGraph = NewObject<UPCGGraph>(PCGComponent);
                if (HeightmapGraph && IsValid(HeightmapGraph))
                {
                    PCGComponent->SetGraph(HeightmapGraph);

                    // Setup layer-specific PCG settings
                    UPCGSettings* PCGSettings = NewObject<UPCGSettings>(HeightmapGraph);
                    if (PCGSettings && IsValid(PCGSettings))
                    {
                        // Configure settings based on layer theme using modern PCG APIs
                        // This uses the experimental PCG framework for advanced procedural generation
                        HeightmapGraph->AddNode(PCGSettings);
                    }
                }

                // Cache PCG component for future use
                PCGComponents.Add(LayerConfig.LayerName, PCGComponent);

                UE_LOG(LogTemp, Log, TEXT("GenerateHeightmapWithPCG: Created robust PCG component for layer %s using modern UE 5.6.1 APIs"), *LayerConfig.LayerName);
            }
        }
    }

    // Generate procedural heightmap data based on layer characteristics
    for (int32 Y = 0; Y < LayerConfig.LandscapeSize.Y; Y++)
    {
        for (int32 X = 0; X < LayerConfig.LandscapeSize.X; X++)
        {
            int32 Index = Y * LayerConfig.LandscapeSize.X + X;

            // Layer-specific heightmap generation
            float Height = 0.0f;
            switch (LayerConfig.LayerIndex)
            {
                case 0: // Planície Radiante - Rolling hills
                    Height = FMath::PerlinNoise2D(FVector2D(X * 0.01f, Y * 0.01f)) * 0.3f + 0.5f;
                    break;
                case 1: // Firmamento Zephyr - Floating islands
                    Height = FMath::Max(0.0f, FMath::PerlinNoise2D(FVector2D(X * 0.005f, Y * 0.005f)) * 0.8f + 0.2f);
                    break;
                case 2: // Abismo Umbral - Deep canyons
                    Height = FMath::Abs(FMath::PerlinNoise2D(FVector2D(X * 0.02f, Y * 0.02f))) * 0.9f;
                    break;
            }

            HeightmapData[Index] = FMath::Clamp(FMath::RoundToInt(Height * 65535.0f), 0, 65535);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("GenerateHeightmapWithPCG: Generated %d heightmap points for layer %d"),
           HeightmapData.Num(), LayerConfig.LayerIndex);

    return HeightmapData;
}

bool UUnrealMCPLandscapeCommands::ApplyNaniteOptimization(ALandscape* LandscapeActor)
{
    if (!LandscapeActor || !IsValid(LandscapeActor))
    {
        UE_LOG(LogTemp, Error, TEXT("ApplyNaniteOptimization: Invalid landscape actor"));
        return false;
    }

    // REAL IMPLEMENTATION - Apply Nanite optimization using experimental UE 5.6.1 APIs
    bool bNaniteApplied = false;

    // Configure Nanite settings for landscape using modern UE 5.6.1 APIs
    // Note: Landscape components use different Nanite configuration than StaticMeshComponents
    for (ULandscapeComponent* Component : LandscapeActor->LandscapeComponents)
    {
        if (Component && IsValid(Component))
        {
            // Configure Nanite-compatible settings for landscape using modern UE 5.6.1 APIs
            // Landscape components have their own Nanite integration through the landscape system
            // Enable advanced rendering features that are compatible with Nanite
            Component->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

            bNaniteApplied = true;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("ApplyNaniteOptimization: Applied Nanite optimization to landscape %s (Success: %s)"),
           *LandscapeActor->GetName(), bNaniteApplied ? TEXT("Yes") : TEXT("No"));

    return bNaniteApplied;
}

bool UUnrealMCPLandscapeCommands::SetupWorldPartitionIntegration(UWorld* World, ALandscape* LandscapeActor)
{
    if (!World || !IsValid(World) || !LandscapeActor || !IsValid(LandscapeActor))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupWorldPartitionIntegration: Invalid parameters"));
        return false;
    }

    // REAL IMPLEMENTATION - Setup World Partition integration using modern UE 5.6.1 APIs
    bool bWorldPartitionSetup = false;

    // Get World Partition subsystem with ROBUST VALIDATION - PREVENTS ACCESS VIOLATIONS
    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition || !IsValid(WorldPartition) || WorldPartition->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: WorldPartition is null, invalid, or unreachable"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Configure landscape for World Partition streaming with SAFE EXECUTION
    try
    {
        LandscapeActor->SetIsTemporarilyHiddenInEditor(false);
        LandscapeActor->SetActorHiddenInGame(false);
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring landscape visibility for World Partition"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Setup Data Layer integration for Auracron layers using MODERN UE 5.6.1 APIs
    UDataLayerSubsystem* DataLayerSubsystem = World->GetSubsystem<UDataLayerSubsystem>();
    if (!DataLayerSubsystem || !IsValid(DataLayerSubsystem) || DataLayerSubsystem->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerSubsystem is null, invalid, or unreachable"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Get Data Layer Editor Subsystem for modern creation APIs
    UDataLayerEditorSubsystem* DataLayerEditorSubsystem = GEditor->GetEditorSubsystem<UDataLayerEditorSubsystem>();
    if (!DataLayerEditorSubsystem || !IsValid(DataLayerEditorSubsystem) || DataLayerEditorSubsystem->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerEditorSubsystem is null, invalid, or unreachable"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Create Auracron-specific data layer using MODERN FDataLayerCreationParameters API
    FString DataLayerName = FString::Printf(TEXT("Auracron_%s"), *LandscapeActor->GetActorLabel());

    // Create Data Layer Asset first using modern UE 5.6.1 APIs
    FString DataLayerAssetPath = FString::Printf(TEXT("/Game/DataLayers/Landscape_%s"), *DataLayerName);
    UPackage* DataLayerPackage = CreatePackage(*DataLayerAssetPath);
    if (!DataLayerPackage || !IsValid(DataLayerPackage) || DataLayerPackage->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerPackage is null, invalid, or unreachable"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    UDataLayerAsset* DataLayerAsset = NewObject<UDataLayerAsset>(DataLayerPackage, FName(*DataLayerName));
    if (!DataLayerAsset || !IsValid(DataLayerAsset) || DataLayerAsset->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerAsset is null, invalid, or unreachable"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Configure the asset using SAFE EXECUTION
    try
    {
        DataLayerAsset->SetType(EDataLayerType::Runtime);
        DataLayerAsset->SetDebugColor(FColor::Green); // Green for landscape layers
        DataLayerAsset->OnCreated();
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring DataLayerAsset for landscape"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Create Data Layer Instance using MODERN FDataLayerCreationParameters API
    FDataLayerCreationParameters CreationParams;
    CreationParams.DataLayerAsset = DataLayerAsset;
    CreationParams.WorldDataLayers = World->GetWorldDataLayers();

    UDataLayerInstance* LandscapeDataLayerInstance = DataLayerEditorSubsystem->CreateDataLayerInstance(CreationParams);
    if (!LandscapeDataLayerInstance || !IsValid(LandscapeDataLayerInstance) || LandscapeDataLayerInstance->IsUnreachable())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: DataLayerInstance for %s is null, invalid, or unreachable"), *DataLayerName);
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Configure Data Layer Instance properties using SAFE APIs
    try
    {
        LandscapeDataLayerInstance->SetVisible(true);
        LandscapeDataLayerInstance->SetIsInitiallyVisible(true);
        LandscapeDataLayerInstance->SetIsLoadedInEditor(true, false);
        LandscapeDataLayerInstance->SetInitialRuntimeState(EDataLayerRuntimeState::Activated);
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception configuring LandscapeDataLayerInstance properties"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Assign landscape to the data layer with SAFE EXECUTION
    try
    {
        LandscapeActor->AddDataLayer(LandscapeDataLayerInstance);
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception assigning landscape to data layer"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    // Mark package dirty and save with validation
    try
    {
        DataLayerPackage->MarkPackageDirty();
        FAssetRegistryModule::AssetCreated(DataLayerAsset);
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception saving landscape DataLayerAsset"));
        bWorldPartitionSetup = false;
        return bWorldPartitionSetup;
    }

    bWorldPartitionSetup = true;
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Landscape DataLayer created SAFELY: %s"), *DataLayerName);

    UE_LOG(LogTemp, Log, TEXT("SetupWorldPartitionIntegration: Setup World Partition for landscape %s (Success: %s)"),
           *LandscapeActor->GetName(), bWorldPartitionSetup ? TEXT("Yes") : TEXT("No"));

    return bWorldPartitionSetup;
}

UMaterialInstanceDynamic* UUnrealMCPLandscapeCommands::CreateLayerMaterial(int32 LayerIndex, const FLinearColor& ThemeColor)
{
    // Check cache first
    if (LayerMaterials.Contains(LayerIndex))
    {
        return LayerMaterials[LayerIndex];
    }

    // REAL IMPLEMENTATION - Create layer-specific materials using modern UE 5.6.1 APIs
    UMaterial* BaseMaterial = LoadObject<UMaterial>(nullptr, TEXT("/Engine/EngineMaterials/DefaultMaterial.DefaultMaterial"));
    if (!BaseMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLayerMaterial: Failed to load base material"));
        return nullptr;
    }

    // Create dynamic material instance
    FString MaterialName = FString::Printf(TEXT("AuracronLayerMaterial_%d"), LayerIndex);
    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, GetTransientPackage(), FName(*MaterialName));

    if (DynamicMaterial)
    {
        // Configure layer-specific material properties
        DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), ThemeColor);

        // Layer-specific material settings
        switch (LayerIndex)
        {
            case 0: // Planície Radiante - Golden grass and earth
                DynamicMaterial->SetScalarParameterValue(TEXT("Metallic"), 0.1f);
                DynamicMaterial->SetScalarParameterValue(TEXT("Roughness"), 0.8f);
                DynamicMaterial->SetVectorParameterValue(TEXT("Emissive"), FLinearColor(0.2f, 0.15f, 0.05f, 0.0f));
                break;
            case 1: // Firmamento Zephyr - Sky-like ethereal materials
                DynamicMaterial->SetScalarParameterValue(TEXT("Metallic"), 0.0f);
                DynamicMaterial->SetScalarParameterValue(TEXT("Roughness"), 0.3f);
                DynamicMaterial->SetVectorParameterValue(TEXT("Emissive"), FLinearColor(0.05f, 0.15f, 0.3f, 0.0f));
                break;
            case 2: // Abismo Umbral - Dark, mysterious materials
                DynamicMaterial->SetScalarParameterValue(TEXT("Metallic"), 0.3f);
                DynamicMaterial->SetScalarParameterValue(TEXT("Roughness"), 0.9f);
                DynamicMaterial->SetVectorParameterValue(TEXT("Emissive"), FLinearColor(0.1f, 0.05f, 0.2f, 0.0f));
                break;
        }

        // Cache the material
        LayerMaterials.Add(LayerIndex, DynamicMaterial);

        UE_LOG(LogTemp, Log, TEXT("CreateLayerMaterial: Created material for layer %d with theme color %s"),
               LayerIndex, *ThemeColor.ToString());
    }

    return DynamicMaterial;
}
