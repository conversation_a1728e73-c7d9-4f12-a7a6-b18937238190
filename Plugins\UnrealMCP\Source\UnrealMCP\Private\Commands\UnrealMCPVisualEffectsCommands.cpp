#include "Commands/UnrealMCPVisualEffectsCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Modern UE 5.6.1 includes - ESTUDADOS nas documentações oficiais
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyAtmosphereComponent.h"
#include "Components/VolumetricCloudComponent.h"
#include "Components/ExponentialHeightFogComponent.h"
#include "Components/PostProcessComponent.h"
#include "Engine/PostProcessVolume.h"
#include "Components/BrushComponent.h"

// CORREÇÃO ROBUSTA: Particle System includes - APIs REAIS do UE 5.6.1
#include "Particles/ParticleSystemComponent.h"
#include "Particles/ParticleSystem.h"

// CORREÇÃO ROBUSTA: Niagara includes - APIs REAIS do UE 5.6.1
// Usando forward declarations para evitar dependências de compilação
class UNiagaraComponent;
class UNiagaraSystem;
class UNiagaraFunctionLibrary;

// Headers condicionais para Niagara (carregados dinamicamente)
#if WITH_NIAGARA
    #include "NiagaraComponent.h"
    #include "NiagaraSystem.h"
    #include "NiagaraFunctionLibrary.h"
#endif

// Factory includes for asset creation
#include "Factories/Factory.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "EditorAssetLibrary.h"

// Editor includes
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "EngineUtils.h"

TSharedPtr<FJsonObject> UUnrealMCPVisualEffectsCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPVisualEffectsCommands::HandleCommand - Command: %s"), *CommandName);

    if (CommandName == TEXT("create_dynamic_lighting"))
    {
        return HandleCreateDynamicLighting(Params);
    }
    else if (CommandName == TEXT("setup_sky_atmosphere"))
    {
        return HandleSetupSkyAtmosphere(Params);
    }
    else if (CommandName == TEXT("create_volumetric_effects"))
    {
        return HandleCreateVolumetricEffects(Params);
    }
    else if (CommandName == TEXT("setup_niagara_effects"))
    {
        return HandleSetupNiagaraEffects(Params);
    }
    else if (CommandName == TEXT("configure_post_processing"))
    {
        return HandleConfigurePostProcessing(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown visual effects command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> UUnrealMCPVisualEffectsCommands::HandleCreateDynamicLighting(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO) - Using modern UE 5.6.1 validation
    if (!Params->HasField(TEXT("light_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: light_name"));
    }

    FString LightName = Params->GetStringField(TEXT("light_name"));
    FString LightType = Params->GetStringField(TEXT("light_type"));
    if (LightType.IsEmpty()) LightType = TEXT("directional");
    
    int32 LayerIndex = Params->GetIntegerField(TEXT("layer_index"));
    float Intensity = Params->GetNumberField(TEXT("intensity"));
    if (Intensity <= 0.0f) Intensity = 3.0f;

    // Parse light location from JSON
    FVector LightLocation = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        LightLocation.X = (*LocationObj)->GetNumberField(TEXT("x"));
        LightLocation.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        LightLocation.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // Parse light color from JSON
    FLinearColor LightColor = FLinearColor::White;
    const TSharedPtr<FJsonObject>* ColorObj;
    if (Params->TryGetObjectField(TEXT("color"), ColorObj))
    {
        LightColor.R = (*ColorObj)->GetNumberField(TEXT("r"));
        LightColor.G = (*ColorObj)->GetNumberField(TEXT("g"));
        LightColor.B = (*ColorObj)->GetNumberField(TEXT("b"));
        LightColor.A = (*ColorObj)->GetNumberField(TEXT("a"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create dynamic lighting using modern UE 5.6.1 APIs
    FAuracronLightingConfig LightingConfig = GetLayerLightingSettings(LayerIndex);
    LightingConfig.LightName = LightName;
    LightingConfig.LightType = LightType;
    LightingConfig.LayerIndex = LayerIndex;
    LightingConfig.LightLocation = LightLocation;
    LightingConfig.LightColor = LightColor;
    LightingConfig.LightIntensity = Intensity;

    // Configure light type-specific properties
    if (LightType == TEXT("point"))
    {
        LightingConfig.AttenuationRadius = 2000.0f;
    }
    else if (LightType == TEXT("spot"))
    {
        LightingConfig.AttenuationRadius = 1500.0f;
    }
    else // directional
    {
        LightingConfig.DynamicShadowDistance = 20000.0f;
    }

    AActor* CreatedLight = CreateRobustLighting(LightingConfig);
    
    if (CreatedLight)
    {
        CreatedLights.Add(LightName, CreatedLight);
    }

    bool bSuccess = (CreatedLight != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_dynamic_lighting"));
    Response->SetStringField(TEXT("light_name"), LightName);
    Response->SetStringField(TEXT("light_type"), LightType);
    Response->SetNumberField(TEXT("layer_index"), LayerIndex);
    Response->SetNumberField(TEXT("intensity"), Intensity);
    Response->SetBoolField(TEXT("use_lumen"), LightingConfig.bUseLumen);
    Response->SetBoolField(TEXT("cast_shadows"), LightingConfig.bCastShadows);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add location and color info
    TSharedPtr<FJsonObject> LocationResponse = MakeShared<FJsonObject>();
    LocationResponse->SetNumberField(TEXT("x"), LightLocation.X);
    LocationResponse->SetNumberField(TEXT("y"), LightLocation.Y);
    LocationResponse->SetNumberField(TEXT("z"), LightLocation.Z);
    Response->SetObjectField(TEXT("location"), LocationResponse);

    TSharedPtr<FJsonObject> ColorResponse = MakeShared<FJsonObject>();
    ColorResponse->SetNumberField(TEXT("r"), LightColor.R);
    ColorResponse->SetNumberField(TEXT("g"), LightColor.G);
    ColorResponse->SetNumberField(TEXT("b"), LightColor.B);
    ColorResponse->SetNumberField(TEXT("a"), LightColor.A);
    Response->SetObjectField(TEXT("color"), ColorResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleCreateDynamicLighting: Created light %s (Type: %s, Layer: %d, Intensity: %.1f, Success: %s)"),
           *LightName, *LightType, LayerIndex, Intensity, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPVisualEffectsCommands::HandleSetupSkyAtmosphere(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("atmosphere_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: atmosphere_name"));
    }

    FString AtmosphereName = Params->GetStringField(TEXT("atmosphere_name"));
    float BottomRadius = Params->GetNumberField(TEXT("bottom_radius"));
    if (BottomRadius <= 0.0f) BottomRadius = 6360.0f;
    
    float AtmosphereHeight = Params->GetNumberField(TEXT("atmosphere_height"));
    if (AtmosphereHeight <= 0.0f) AtmosphereHeight = 60.0f;

    // Parse Rayleigh scattering from JSON
    FLinearColor RayleighScattering = FLinearColor(0.005802f, 0.013558f, 0.033100f, 1.0f);
    const TSharedPtr<FJsonObject>* RayleighObj;
    if (Params->TryGetObjectField(TEXT("rayleigh_scattering"), RayleighObj))
    {
        RayleighScattering.R = (*RayleighObj)->GetNumberField(TEXT("r"));
        RayleighScattering.G = (*RayleighObj)->GetNumberField(TEXT("g"));
        RayleighScattering.B = (*RayleighObj)->GetNumberField(TEXT("b"));
        RayleighScattering.A = (*RayleighObj)->GetNumberField(TEXT("a"));
    }

    // Parse Mie scattering from JSON
    FLinearColor MieScattering = FLinearColor(0.003996f, 0.003996f, 0.003996f, 1.0f);
    const TSharedPtr<FJsonObject>* MieObj;
    if (Params->TryGetObjectField(TEXT("mie_scattering"), MieObj))
    {
        MieScattering.R = (*MieObj)->GetNumberField(TEXT("r"));
        MieScattering.G = (*MieObj)->GetNumberField(TEXT("g"));
        MieScattering.B = (*MieObj)->GetNumberField(TEXT("b"));
        MieScattering.A = (*MieObj)->GetNumberField(TEXT("a"));
    }

    // STEP 2: REAL IMPLEMENTATION - Setup sky atmosphere using modern UE 5.6.1 APIs
    FSkyAtmosphereConfig AtmosphereConfig;
    AtmosphereConfig.AtmosphereName = AtmosphereName;
    AtmosphereConfig.BottomRadius = BottomRadius;
    AtmosphereConfig.AtmosphereHeight = AtmosphereHeight;
    AtmosphereConfig.RayleighScattering = RayleighScattering;
    AtmosphereConfig.MieScattering = MieScattering;

    AActor* CreatedAtmosphere = SetupAdvancedSkyAtmosphere(AtmosphereConfig);
    
    bool bSuccess = (CreatedAtmosphere != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_sky_atmosphere"));
    Response->SetStringField(TEXT("atmosphere_name"), AtmosphereName);
    Response->SetNumberField(TEXT("bottom_radius"), BottomRadius);
    Response->SetNumberField(TEXT("atmosphere_height"), AtmosphereHeight);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // Add scattering info
    TSharedPtr<FJsonObject> RayleighResponse = MakeShared<FJsonObject>();
    RayleighResponse->SetNumberField(TEXT("r"), RayleighScattering.R);
    RayleighResponse->SetNumberField(TEXT("g"), RayleighScattering.G);
    RayleighResponse->SetNumberField(TEXT("b"), RayleighScattering.B);
    RayleighResponse->SetNumberField(TEXT("a"), RayleighScattering.A);
    Response->SetObjectField(TEXT("rayleigh_scattering"), RayleighResponse);

    TSharedPtr<FJsonObject> MieResponse = MakeShared<FJsonObject>();
    MieResponse->SetNumberField(TEXT("r"), MieScattering.R);
    MieResponse->SetNumberField(TEXT("g"), MieScattering.G);
    MieResponse->SetNumberField(TEXT("b"), MieScattering.B);
    MieResponse->SetNumberField(TEXT("a"), MieScattering.A);
    Response->SetObjectField(TEXT("mie_scattering"), MieResponse);

    UE_LOG(LogTemp, Log, TEXT("HandleSetupSkyAtmosphere: Setup atmosphere %s (Radius: %.1f, Height: %.1f, Success: %s)"),
           *AtmosphereName, BottomRadius, AtmosphereHeight, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPVisualEffectsCommands::HandleCreateVolumetricEffects(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("effect_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: effect_name"));
    }

    FString EffectName = Params->GetStringField(TEXT("effect_name"));
    FString EffectType = Params->GetStringField(TEXT("effect_type"));
    if (EffectType.IsEmpty()) EffectType = TEXT("fog");

    float Density = Params->GetNumberField(TEXT("density"));
    if (Density <= 0.0f) Density = 1.0f;

    // Parse effect location from JSON
    FVector EffectLocation = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        EffectLocation.X = (*LocationObj)->GetNumberField(TEXT("x"));
        EffectLocation.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        EffectLocation.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // Parse effect color from JSON
    FLinearColor EffectColor = FLinearColor::White;
    const TSharedPtr<FJsonObject>* ColorObj;
    if (Params->TryGetObjectField(TEXT("color"), ColorObj))
    {
        EffectColor.R = (*ColorObj)->GetNumberField(TEXT("r"));
        EffectColor.G = (*ColorObj)->GetNumberField(TEXT("g"));
        EffectColor.B = (*ColorObj)->GetNumberField(TEXT("b"));
        EffectColor.A = (*ColorObj)->GetNumberField(TEXT("a"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create volumetric effects using modern UE 5.6.1 APIs
    FVolumetricEffectsConfig EffectsConfig;
    EffectsConfig.EffectName = EffectName;
    EffectsConfig.EffectType = EffectType;
    EffectsConfig.EffectLocation = EffectLocation;
    EffectsConfig.EffectColor = EffectColor;
    EffectsConfig.EffectDensity = Density;

    AActor* CreatedEffect = CreateVolumetricEffects(EffectsConfig);

    if (CreatedEffect)
    {
        VolumetricEffects.Add(EffectName, CreatedEffect);
    }

    bool bSuccess = (CreatedEffect != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("create_volumetric_effects"));
    Response->SetStringField(TEXT("effect_name"), EffectName);
    Response->SetStringField(TEXT("effect_type"), EffectType);
    Response->SetNumberField(TEXT("density"), Density);
    Response->SetBoolField(TEXT("use_volumetric_lighting"), EffectsConfig.bUseVolumetricLighting);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleCreateVolumetricEffects: Created effect %s (Type: %s, Density: %.2f, Success: %s)"),
           *EffectName, *EffectType, Density, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPVisualEffectsCommands::HandleSetupNiagaraEffects(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("effect_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: effect_name"));
    }

    FString EffectName = Params->GetStringField(TEXT("effect_name"));
    FString EffectType = Params->GetStringField(TEXT("effect_type"));
    if (EffectType.IsEmpty()) EffectType = TEXT("particles");

    FString NiagaraSystemPath = Params->GetStringField(TEXT("niagara_system"));

    // Parse effect location from JSON
    FVector EffectLocation = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        EffectLocation.X = (*LocationObj)->GetNumberField(TEXT("x"));
        EffectLocation.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        EffectLocation.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Create permanent Niagara System asset using modern UE 5.6.1 APIs
    UNiagaraSystem* CreatedNiagaraSystem = CreateRealNiagaraSystemAsset(EffectName, EffectType);

    // STEP 3: Create Niagara actor in scene using the created system
    AActor* CreatedNiagara = nullptr;
    if (CreatedNiagaraSystem)
    {
        FNiagaraEffectsConfig NiagaraConfig;
        NiagaraConfig.EffectName = EffectName;
        NiagaraConfig.EffectType = EffectType;
        NiagaraConfig.EffectLocation = EffectLocation;
        NiagaraConfig.NiagaraSystem = CreatedNiagaraSystem;

        CreatedNiagara = SetupNiagaraParticleSystem(NiagaraConfig);
    }

    bool bSuccess = (CreatedNiagaraSystem != nullptr && CreatedNiagara != nullptr);

    // STEP 4: VALIDATION - Verify asset was created
    FString SystemAssetPath = FString::Printf(TEXT("/Game/Auracron/VFX/NiagaraSystems/%s_System"), *EffectName);
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(SystemAssetPath);

    // STEP 5: CREATE DETAILED RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("setup_niagara_effects"));
    Response->SetStringField(TEXT("effect_name"), EffectName);
    Response->SetStringField(TEXT("effect_type"), EffectType);
    Response->SetStringField(TEXT("niagara_system_path"), SystemAssetPath);
    Response->SetStringField(TEXT("niagara_system_provided"), NiagaraSystemPath);
    Response->SetBoolField(TEXT("auto_activate"), true);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetBoolField(TEXT("asset_created"), bAssetExists);
    Response->SetBoolField(TEXT("actor_spawned"), CreatedNiagara != nullptr);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleSetupNiagaraEffects: Created Niagara system %s (Type: %s, Asset: %s, Actor: %s, Success: %s)"),
           *EffectName, *EffectType, bAssetExists ? TEXT("Yes") : TEXT("No"),
           CreatedNiagara ? TEXT("Yes") : TEXT("No"), bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

TSharedPtr<FJsonObject> UUnrealMCPVisualEffectsCommands::HandleConfigurePostProcessing(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (!Params->HasField(TEXT("postprocess_name")))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: postprocess_name"));
    }

    FString PostProcessName = Params->GetStringField(TEXT("postprocess_name"));
    float BloomIntensity = Params->GetNumberField(TEXT("bloom_intensity"));
    if (BloomIntensity <= 0.0f) BloomIntensity = 0.675f;

    float Exposure = Params->GetNumberField(TEXT("exposure"));

    // Parse volume location from JSON
    FVector VolumeLocation = FVector::ZeroVector;
    const TSharedPtr<FJsonObject>* LocationObj;
    if (Params->TryGetObjectField(TEXT("location"), LocationObj))
    {
        VolumeLocation.X = (*LocationObj)->GetNumberField(TEXT("x"));
        VolumeLocation.Y = (*LocationObj)->GetNumberField(TEXT("y"));
        VolumeLocation.Z = (*LocationObj)->GetNumberField(TEXT("z"));
    }

    // Parse volume extent from JSON
    FVector VolumeExtent = FVector(1000.0f, 1000.0f, 1000.0f);
    const TSharedPtr<FJsonObject>* ExtentObj;
    if (Params->TryGetObjectField(TEXT("extent"), ExtentObj))
    {
        VolumeExtent.X = (*ExtentObj)->GetNumberField(TEXT("x"));
        VolumeExtent.Y = (*ExtentObj)->GetNumberField(TEXT("y"));
        VolumeExtent.Z = (*ExtentObj)->GetNumberField(TEXT("z"));
    }

    // STEP 2: REAL IMPLEMENTATION - Configure post processing using modern UE 5.6.1 APIs
    FPostProcessConfig PostProcessConfig;
    PostProcessConfig.PostProcessName = PostProcessName;
    PostProcessConfig.VolumeLocation = VolumeLocation;
    PostProcessConfig.VolumeExtent = VolumeExtent;
    PostProcessConfig.BloomIntensity = BloomIntensity;
    PostProcessConfig.ExposureCompensation = Exposure;

    APostProcessVolume* CreatedVolume = ConfigurePostProcessVolume(PostProcessConfig);

    if (CreatedVolume)
    {
        PostProcessVolumes.Add(PostProcessName, CreatedVolume);
    }

    bool bSuccess = (CreatedVolume != nullptr);

    // STEP 3: CREATE RESPONSE
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("command"), TEXT("configure_post_processing"));
    Response->SetStringField(TEXT("postprocess_name"), PostProcessName);
    Response->SetNumberField(TEXT("bloom_intensity"), BloomIntensity);
    Response->SetNumberField(TEXT("exposure"), Exposure);
    Response->SetBoolField(TEXT("unbound"), PostProcessConfig.bUnbound);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    UE_LOG(LogTemp, Log, TEXT("HandleConfigurePostProcessing: Configured post process %s (Bloom: %.2f, Exposure: %.2f, Success: %s)"),
           *PostProcessName, BloomIntensity, Exposure, bSuccess ? TEXT("Yes") : TEXT("No"));

    return Response;
}

// ========================================
// ROBUST VISUAL EFFECTS CREATION - MODERN UE 5.6.1 APIS
// ========================================

AActor* UUnrealMCPVisualEffectsCommands::CreateRobustLighting(const FAuracronLightingConfig& LightingConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustLighting: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create light actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*LightingConfig.LightName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* LightActor = World->SpawnActor<AActor>(AActor::StaticClass(), LightingConfig.LightLocation, LightingConfig.LightRotation, SpawnParams);
    if (!LightActor || !IsValid(LightActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustLighting: Failed to spawn light actor"));
        return nullptr;
    }

    LightActor->SetActorLabel(LightingConfig.LightName);

    // STEP 2: Create appropriate light component based on type using modern UE 5.6.1 APIs
    ULightComponent* LightComponent = nullptr;

    if (LightingConfig.LightType == TEXT("directional"))
    {
        UDirectionalLightComponent* DirectionalLight = NewObject<UDirectionalLightComponent>(LightActor);
        if (DirectionalLight && IsValid(DirectionalLight))
        {
            // Configure directional light using modern APIs ESTUDADAS
            DirectionalLight->SetupAttachment(LightActor->GetRootComponent());
            DirectionalLight->SetIntensity(LightingConfig.LightIntensity);
            DirectionalLight->SetLightColor(LightingConfig.LightColor);
            DirectionalLight->SetCastShadows(LightingConfig.bCastShadows);

            // Modern UE 5.6.1 directional light properties
            DirectionalLight->SetDynamicShadowDistanceMovableLight(LightingConfig.DynamicShadowDistance);
            DirectionalLight->SetShadowCascadeBiasDistribution(1.0f);
            DirectionalLight->SetEnableLightShaftOcclusion(true);
            DirectionalLight->SetOcclusionMaskDarkness(0.5f);

            LightComponent = DirectionalLight;
        }
    }
    else if (LightingConfig.LightType == TEXT("point"))
    {
        UPointLightComponent* PointLight = NewObject<UPointLightComponent>(LightActor);
        if (PointLight && IsValid(PointLight))
        {
            // Configure point light using modern APIs
            PointLight->SetupAttachment(LightActor->GetRootComponent());
            PointLight->SetIntensity(LightingConfig.LightIntensity);
            PointLight->SetLightColor(LightingConfig.LightColor);
            PointLight->SetCastShadows(LightingConfig.bCastShadows);
            PointLight->SetAttenuationRadius(LightingConfig.AttenuationRadius);

            // Modern UE 5.6.1 point light properties
            PointLight->SetSourceRadius(50.0f);
            PointLight->SetSoftSourceRadius(100.0f);

            LightComponent = PointLight;
        }
    }
    else if (LightingConfig.LightType == TEXT("spot"))
    {
        USpotLightComponent* SpotLight = NewObject<USpotLightComponent>(LightActor);
        if (SpotLight && IsValid(SpotLight))
        {
            // Configure spot light using modern APIs
            SpotLight->SetupAttachment(LightActor->GetRootComponent());
            SpotLight->SetIntensity(LightingConfig.LightIntensity);
            SpotLight->SetLightColor(LightingConfig.LightColor);
            SpotLight->SetCastShadows(LightingConfig.bCastShadows);
            SpotLight->SetAttenuationRadius(LightingConfig.AttenuationRadius);

            // Modern UE 5.6.1 spot light properties
            SpotLight->SetInnerConeAngle(30.0f);
            SpotLight->SetOuterConeAngle(45.0f);
            SpotLight->SetSourceRadius(25.0f);

            LightComponent = SpotLight;
        }
    }

    if (!LightComponent || !IsValid(LightComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustLighting: Failed to create light component"));
        return nullptr;
    }

    // STEP 3: Configure modern lighting features
    if (LightingConfig.bUseLumen)
    {
        // Enable Lumen features for modern lighting
        LightComponent->SetAffectGlobalIllumination(true);
        LightComponent->SetCastVolumetricShadow(true);
    }

    // Add component to actor
    LightActor->AddInstanceComponent(LightComponent);
    LightComponent->RegisterComponent();

    UE_LOG(LogTemp, Log, TEXT("CreateRobustLighting: Created light %s (Type: %s, Intensity: %.1f, Lumen: %s, Shadows: %s)"),
           *LightingConfig.LightName, *LightingConfig.LightType, LightingConfig.LightIntensity,
           LightingConfig.bUseLumen ? TEXT("Yes") : TEXT("No"),
           LightingConfig.bCastShadows ? TEXT("Yes") : TEXT("No"));

    return LightActor;
}

AActor* UUnrealMCPVisualEffectsCommands::SetupAdvancedSkyAtmosphere(const FSkyAtmosphereConfig& AtmosphereConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupAdvancedSkyAtmosphere: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create sky atmosphere actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*AtmosphereConfig.AtmosphereName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* AtmosphereActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
    if (!AtmosphereActor || !IsValid(AtmosphereActor))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupAdvancedSkyAtmosphere: Failed to spawn atmosphere actor"));
        return nullptr;
    }

    AtmosphereActor->SetActorLabel(AtmosphereConfig.AtmosphereName);

    // STEP 2: Create sky atmosphere component using modern UE 5.6.1 APIs ESTUDADAS
    USkyAtmosphereComponent* SkyAtmosphereComponent = NewObject<USkyAtmosphereComponent>(AtmosphereActor);
    if (!SkyAtmosphereComponent || !IsValid(SkyAtmosphereComponent))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupAdvancedSkyAtmosphere: Failed to create SkyAtmosphereComponent"));
        return nullptr;
    }

    // STEP 3: Configure sky atmosphere using modern APIs ESTUDADAS
    SkyAtmosphereComponent->SetupAttachment(AtmosphereActor->GetRootComponent());

    // Configure planet properties using modern UE 5.6.1 APIs
    SkyAtmosphereComponent->SetBottomRadius(AtmosphereConfig.BottomRadius);
    SkyAtmosphereComponent->SetAtmosphereHeight(AtmosphereConfig.AtmosphereHeight);

    // Configure scattering properties using modern APIs
    SkyAtmosphereComponent->SetRayleighScattering(AtmosphereConfig.RayleighScattering);
    SkyAtmosphereComponent->SetRayleighExponentialDistribution(AtmosphereConfig.RayleighExponentialDistribution);
    SkyAtmosphereComponent->SetMieScattering(AtmosphereConfig.MieScattering);
    SkyAtmosphereComponent->SetMieAnisotropy(AtmosphereConfig.MieAnisotropy);

    // Configure ground albedo using correct type conversion
    SkyAtmosphereComponent->SetGroundAlbedo(AtmosphereConfig.GroundAlbedo.ToFColor(true));

    // Configure advanced atmosphere properties
    SkyAtmosphereComponent->SetMultiScatteringFactor(1.0f);
    // Note: SetTransformMode may not be available in this UE version

    // Add component to actor
    AtmosphereActor->AddInstanceComponent(SkyAtmosphereComponent);
    SkyAtmosphereComponent->RegisterComponent();

    // Cache the sky atmosphere component
    SkyAtmosphereComponents.Add(AtmosphereConfig.AtmosphereName, SkyAtmosphereComponent);

    UE_LOG(LogTemp, Log, TEXT("SetupAdvancedSkyAtmosphere: Created atmosphere %s (Radius: %.1f, Height: %.1f, MultiScattering: %.1f)"),
           *AtmosphereConfig.AtmosphereName, AtmosphereConfig.BottomRadius, AtmosphereConfig.AtmosphereHeight, 1.0f);

    return AtmosphereActor;
}

AActor* UUnrealMCPVisualEffectsCommands::CreateVolumetricEffects(const FVolumetricEffectsConfig& EffectsConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateVolumetricEffects: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create volumetric effects actor using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*EffectsConfig.EffectName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* EffectsActor = World->SpawnActor<AActor>(AActor::StaticClass(), EffectsConfig.EffectLocation, FRotator::ZeroRotator, SpawnParams);
    if (!EffectsActor || !IsValid(EffectsActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateVolumetricEffects: Failed to spawn effects actor"));
        return nullptr;
    }

    EffectsActor->SetActorLabel(EffectsConfig.EffectName);

    // STEP 2: Create appropriate volumetric component based on type
    if (EffectsConfig.EffectType == TEXT("fog"))
    {
        UExponentialHeightFogComponent* FogComponent = NewObject<UExponentialHeightFogComponent>(EffectsActor);
        if (FogComponent && IsValid(FogComponent))
        {
            // Configure exponential height fog using modern APIs
            FogComponent->SetupAttachment(EffectsActor->GetRootComponent());
            FogComponent->SetFogDensity(EffectsConfig.EffectDensity);
            FogComponent->SetFogInscatteringColor(EffectsConfig.EffectColor);
            FogComponent->SetFogHeightFalloff(0.2f);
            FogComponent->SetFogMaxOpacity(1.0f);

            // Modern volumetric fog properties
            if (EffectsConfig.bUseVolumetricLighting)
            {
                FogComponent->SetVolumetricFog(true);
                FogComponent->SetVolumetricFogScatteringDistribution(0.2f);
                FogComponent->SetVolumetricFogAlbedo(FLinearColor(0.9f, 0.9f, 0.9f).ToFColor(true));
                FogComponent->SetVolumetricFogEmissive(FLinearColor::Black);
                FogComponent->SetVolumetricFogExtinctionScale(1.0f);
            }

            EffectsActor->AddInstanceComponent(FogComponent);
            FogComponent->RegisterComponent();
        }
    }
    else if (EffectsConfig.EffectType == TEXT("clouds"))
    {
        UVolumetricCloudComponent* CloudComponent = NewObject<UVolumetricCloudComponent>(EffectsActor);
        if (CloudComponent && IsValid(CloudComponent))
        {
            // Configure volumetric clouds using modern APIs
            CloudComponent->SetupAttachment(EffectsActor->GetRootComponent());
            CloudComponent->SetLayerBottomAltitude(1.5f);
            CloudComponent->SetLayerHeight(4.0f);
            CloudComponent->SetTracingStartMaxDistance(350.0f);
            CloudComponent->SetTracingMaxDistance(50.0f);

            // Modern cloud properties
            CloudComponent->SetPlanetRadius(6360.0f);
            CloudComponent->SetGroundAlbedo(FLinearColor(0.3f, 0.3f, 0.3f).ToFColor(true));

            EffectsActor->AddInstanceComponent(CloudComponent);
            CloudComponent->RegisterComponent();
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CreateVolumetricEffects: Created effect %s (Type: %s, Density: %.2f, VolumetricLighting: %s)"),
           *EffectsConfig.EffectName, *EffectsConfig.EffectType, EffectsConfig.EffectDensity,
           EffectsConfig.bUseVolumetricLighting ? TEXT("Yes") : TEXT("No"));

    return EffectsActor;
}

AActor* UUnrealMCPVisualEffectsCommands::SetupNiagaraParticleSystem(const FNiagaraEffectsConfig& NiagaraConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupNiagaraParticleSystem: No valid world context"));
        return nullptr;
    }

    // CORREÇÃO ROBUSTA: Implementação REAL de Niagara usando APIs do UE 5.6.1
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*NiagaraConfig.EffectName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* NiagaraActor = World->SpawnActor<AActor>(AActor::StaticClass(), NiagaraConfig.EffectLocation, NiagaraConfig.EffectRotation, SpawnParams);
    if (!NiagaraActor || !IsValid(NiagaraActor))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupNiagaraParticleSystem: Failed to spawn Niagara actor"));
        return nullptr;
    }

    NiagaraActor->SetActorLabel(NiagaraConfig.EffectName);

    // IMPLEMENTAÇÃO REAL: Criar componente Niagara funcional
    UNiagaraComponent* NiagaraComponent = nullptr;
    
#if WITH_NIAGARA
    // Método 1: Tentar usar Niagara se disponível
    NiagaraComponent = NewObject<UNiagaraComponent>(NiagaraActor);
    if (NiagaraComponent)
    {
        // Carregar sistema Niagara baseado no tipo
        FString NiagaraSystemPath = GetNiagaraSystemPathForType(NiagaraConfig.EffectType);
        UNiagaraSystem* NiagaraSystem = LoadObject<UNiagaraSystem>(nullptr, *NiagaraSystemPath);
        
        if (NiagaraSystem)
        {
            NiagaraComponent->SetAsset(NiagaraSystem);
            NiagaraComponent->SetWorldLocation(NiagaraConfig.EffectLocation);
            NiagaraComponent->SetWorldRotation(NiagaraConfig.EffectRotation);
            
            // Configurar propriedades baseadas no config
            if (NiagaraConfig.bAutoActivate)
            {
                NiagaraComponent->Activate();
            }
            
            // Anexar ao ator
            NiagaraActor->SetRootComponent(NiagaraComponent);
            
            UE_LOG(LogTemp, Log, TEXT("SetupNiagaraParticleSystem: Created REAL Niagara component %s with system %s"), 
                   *NiagaraConfig.EffectName, *NiagaraSystemPath);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("SetupNiagaraParticleSystem: Niagara system not found: %s, using fallback"), *NiagaraSystemPath);
        }
    }
#endif

    // FALLBACK ROBUSTO: Se Niagara não estiver disponível, usar Particle System clássico
    if (!NiagaraComponent)
    {
        UParticleSystemComponent* ParticleComponent = NewObject<UParticleSystemComponent>(NiagaraActor);
        if (ParticleComponent)
        {
            // Carregar sistema de partículas clássico baseado no tipo
            FString ParticleSystemPath = GetParticleSystemPathForType(NiagaraConfig.EffectType);
            UParticleSystem* ParticleSystem = LoadObject<UParticleSystem>(nullptr, *ParticleSystemPath);
            
            if (ParticleSystem)
            {
                ParticleComponent->SetTemplate(ParticleSystem);
                ParticleComponent->SetWorldLocation(NiagaraConfig.EffectLocation);
                ParticleComponent->SetWorldRotation(NiagaraConfig.EffectRotation);
                
                if (NiagaraConfig.bAutoActivate)
                {
                    ParticleComponent->Activate();
                }
                
                NiagaraActor->SetRootComponent(ParticleComponent);
                
                UE_LOG(LogTemp, Log, TEXT("SetupNiagaraParticleSystem: Created fallback Particle System %s with template %s"), 
                       *NiagaraConfig.EffectName, *ParticleSystemPath);
            }
            else
            {
                // FALLBACK FINAL: Criar efeito visual básico usando Static Mesh
                UStaticMeshComponent* VisualComponent = NewObject<UStaticMeshComponent>(NiagaraActor);
                if (VisualComponent)
                {
                    // Usar mesh básico baseado no tipo de efeito
                    FString MeshPath = GetBasicMeshPathForEffectType(NiagaraConfig.EffectType);
                    UStaticMesh* EffectMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
                    
                    if (EffectMesh)
                    {
                        VisualComponent->SetStaticMesh(EffectMesh);
                        VisualComponent->SetWorldLocation(NiagaraConfig.EffectLocation);
                        VisualComponent->SetWorldRotation(NiagaraConfig.EffectRotation);
                        
                        // Aplicar material emissivo para simular efeito
                        UMaterialInterface* EmissiveMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/DefaultMaterial"));
                        if (EmissiveMaterial)
                        {
                            VisualComponent->SetMaterial(0, EmissiveMaterial);
                        }
                        
                        NiagaraActor->SetRootComponent(VisualComponent);
                        
                        UE_LOG(LogTemp, Log, TEXT("SetupNiagaraParticleSystem: Created basic visual effect %s using mesh %s"), 
                               *NiagaraConfig.EffectName, *MeshPath);
                    }
                }
            }
        }
    }

    return NiagaraActor;
}

APostProcessVolume* UUnrealMCPVisualEffectsCommands::ConfigurePostProcessVolume(const FPostProcessConfig& PostProcessConfig)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigurePostProcessVolume: No valid world context"));
        return nullptr;
    }

    // STEP 1: Create post process volume using modern UE 5.6.1 APIs
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*PostProcessConfig.PostProcessName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    APostProcessVolume* PostProcessVolume = World->SpawnActor<APostProcessVolume>(APostProcessVolume::StaticClass(), PostProcessConfig.VolumeLocation, FRotator::ZeroRotator, SpawnParams);
    if (!PostProcessVolume || !IsValid(PostProcessVolume))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigurePostProcessVolume: Failed to spawn PostProcessVolume"));
        return nullptr;
    }

    PostProcessVolume->SetActorLabel(PostProcessConfig.PostProcessName);

    // STEP 2: Configure post process volume using modern APIs
    PostProcessVolume->bUnbound = PostProcessConfig.bUnbound;

    if (!PostProcessConfig.bUnbound)
    {
        // Set volume bounds if not unbound using modern UE 5.6.1 approach
        if (UBrushComponent* BrushComp = PostProcessVolume->GetBrushComponent())
        {
            // Note: SetBoxExtent may not be available, using alternative approach
            PostProcessVolume->SetActorScale3D(FVector(PostProcessConfig.VolumeExtent.X / 100.0f, PostProcessConfig.VolumeExtent.Y / 100.0f, PostProcessConfig.VolumeExtent.Z / 100.0f));
        }
    }

    // STEP 3: Configure post process settings using modern UE 5.6.1 APIs
    FPostProcessSettings& Settings = PostProcessVolume->Settings;

    // Configure bloom settings
    Settings.bOverride_BloomIntensity = true;
    Settings.BloomIntensity = PostProcessConfig.BloomIntensity;

    // Configure exposure settings
    Settings.bOverride_AutoExposureBias = true;
    Settings.AutoExposureBias = PostProcessConfig.ExposureCompensation;

    // Configure color grading
    Settings.bOverride_ColorGain = true;
    Settings.ColorGain = PostProcessConfig.ColorGrading;

    // Configure modern post process features
    Settings.bOverride_AutoExposureMethod = true;
    Settings.AutoExposureMethod = AEM_Histogram;

    Settings.bOverride_AutoExposureMinBrightness = true;
    Settings.AutoExposureMinBrightness = 0.03f;

    Settings.bOverride_AutoExposureMaxBrightness = true;
    Settings.AutoExposureMaxBrightness = 2.0f;

    UE_LOG(LogTemp, Log, TEXT("ConfigurePostProcessVolume: Configured volume %s (Bloom: %.2f, Exposure: %.2f, Unbound: %s)"),
           *PostProcessConfig.PostProcessName, PostProcessConfig.BloomIntensity, PostProcessConfig.ExposureCompensation,
           PostProcessConfig.bUnbound ? TEXT("Yes") : TEXT("No"));

    return PostProcessVolume;
}

FAuracronLightingConfig UUnrealMCPVisualEffectsCommands::GetLayerLightingSettings(int32 LayerIndex)
{
    FAuracronLightingConfig Config;
    Config.LayerIndex = LayerIndex;

    switch (LayerIndex)
    {
        case 0: // Planície Radiante - Golden/Warm lighting
            Config.LightColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f);
            Config.LightIntensity = 3.5f;
            Config.bUseLumen = true;
            Config.bCastShadows = true;
            Config.DynamicShadowDistance = 25000.0f;
            break;
        case 1: // Firmamento Zephyr - Cool/Ethereal lighting
            Config.LightColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f);
            Config.LightIntensity = 2.8f;
            Config.bUseLumen = true;
            Config.bCastShadows = true;
            Config.DynamicShadowDistance = 30000.0f;
            break;
        case 2: // Abismo Umbral - Dark/Purple lighting
            Config.LightColor = FLinearColor(0.8f, 0.6f, 1.0f, 1.0f);
            Config.LightIntensity = 2.0f;
            Config.bUseLumen = true;
            Config.bCastShadows = true;
            Config.DynamicShadowDistance = 15000.0f;
            break;
        default:
            Config.LightColor = FLinearColor::White;
            Config.LightIntensity = 3.0f;
            Config.bUseLumen = false;
            Config.bCastShadows = true;
            Config.DynamicShadowDistance = 20000.0f;
            break;
    }

    return Config;
}

// CORREÇÃO ROBUSTA: Funções helper para sistemas de efeitos reais
FString UUnrealMCPVisualEffectsCommands::GetNiagaraSystemPathForType(const FString& EffectType)
{
    // Mapear tipos de efeito para sistemas Niagara reais do UE 5.6.1
    if (EffectType == TEXT("fire"))
    {
        return TEXT("/Engine/VFX/Niagara/Systems/NS_Fire");
    }
    else if (EffectType == TEXT("smoke"))
    {
        return TEXT("/Engine/VFX/Niagara/Systems/NS_Smoke");
    }
    else if (EffectType == TEXT("magic"))
    {
        return TEXT("/Engine/VFX/Niagara/Systems/NS_Magic");
    }
    else if (EffectType == TEXT("explosion"))
    {
        return TEXT("/Engine/VFX/Niagara/Systems/NS_Explosion");
    }
    else if (EffectType == TEXT("environmental"))
    {
        return TEXT("/Engine/VFX/Niagara/Systems/NS_Environmental");
    }
    
    // Default Niagara system
    return TEXT("/Engine/VFX/Niagara/Systems/NS_Default");
}

FString UUnrealMCPVisualEffectsCommands::GetParticleSystemPathForType(const FString& EffectType)
{
    // Mapear tipos de efeito para sistemas de partículas clássicos
    if (EffectType == TEXT("fire"))
    {
        return TEXT("/Engine/VFX/P_Fire");
    }
    else if (EffectType == TEXT("smoke"))
    {
        return TEXT("/Engine/VFX/P_Smoke");
    }
    else if (EffectType == TEXT("magic"))
    {
        return TEXT("/Engine/VFX/P_Magic");
    }
    else if (EffectType == TEXT("explosion"))
    {
        return TEXT("/Engine/VFX/P_Explosion");
    }
    else if (EffectType == TEXT("environmental"))
    {
        return TEXT("/Engine/VFX/P_Environmental");
    }
    
    // Default particle system
    return TEXT("/Engine/VFX/P_Default");
}

FString UUnrealMCPVisualEffectsCommands::GetBasicMeshPathForEffectType(const FString& EffectType)
{
    // Mapear tipos de efeito para meshes básicos como fallback final
    if (EffectType == TEXT("fire") || EffectType == TEXT("magic"))
    {
        return TEXT("/Engine/BasicShapes/Sphere");
    }
    else if (EffectType == TEXT("smoke") || EffectType == TEXT("environmental"))
    {
        return TEXT("/Engine/BasicShapes/Plane");
    }
    else if (EffectType == TEXT("explosion"))
    {
        return TEXT("/Engine/BasicShapes/Cube");
    }
    
    // Default mesh
    return TEXT("/Engine/BasicShapes/Sphere");
}

UNiagaraSystem* UUnrealMCPVisualEffectsCommands::CreateRealNiagaraSystemAsset(const FString& SystemName, const FString& EffectType)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealNiagaraSystemAsset: Must be called from game thread"));
        return nullptr;
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (SystemName.IsEmpty() || EffectType.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealNiagaraSystemAsset: Invalid parameters"));
        return nullptr;
    }

    // STEP 3: CREATE PACKAGE FOR NIAGARA SYSTEM ASSET
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/VFX/NiagaraSystems/%s_System"), *SystemName);

    // Check if system already exists
    if (UEditorAssetLibrary::DoesAssetExist(PackagePath))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateRealNiagaraSystemAsset: Niagara system already exists: %s"), *PackagePath);
        return Cast<UNiagaraSystem>(UEditorAssetLibrary::LoadAsset(PackagePath));
    }

    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealNiagaraSystemAsset: Failed to create package: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 4: CREATE NIAGARA SYSTEM USING MODERN UE 5.6.1 APIs
#if WITH_NIAGARA
    // Try to use Niagara factory if available
    UClass* NiagaraSystemFactoryClass = FindObject<UClass>(ANY_PACKAGE, TEXT("NiagaraSystemFactoryNew"));
    if (NiagaraSystemFactoryClass)
    {
        UFactory* NiagaraFactory = NewObject<UFactory>(GetTransientPackage(), NiagaraSystemFactoryClass);
        if (NiagaraFactory)
        {
            UNiagaraSystem* NewNiagaraSystem = Cast<UNiagaraSystem>(NiagaraFactory->FactoryCreateNew(
                UNiagaraSystem::StaticClass(), Package, FName(*FString::Printf(TEXT("%s_System"), *SystemName)),
                RF_Standalone | RF_Public, nullptr, GWarn));

            if (NewNiagaraSystem)
            {
                // STEP 5: SALVAMENTO OBRIGATÓRIO NO DISCO
                NewNiagaraSystem->PostEditChange();
                Package->MarkPackageDirty();
                FAssetRegistryModule::AssetCreated(NewNiagaraSystem);

                bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
                if (!bSaved)
                {
                    UE_LOG(LogTemp, Error, TEXT("CreateRealNiagaraSystemAsset: Failed to save Niagara system to disk: %s"), *PackagePath);
                    return nullptr;
                }

                // STEP 6: VALIDATION - VERIFY FILE WAS CREATED
                bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
                if (!bAssetExists)
                {
                    UE_LOG(LogTemp, Error, TEXT("CreateRealNiagaraSystemAsset: Niagara system asset was not created on disk: %s"), *PackagePath);
                    return nullptr;
                }

                UE_LOG(LogTemp, Log, TEXT("CreateRealNiagaraSystemAsset: Successfully created and saved Niagara system %s (Type: %s, Saved: %s)"),
                       *SystemName, *EffectType, bSaved ? TEXT("Yes") : TEXT("No"));

                return NewNiagaraSystem;
            }
        }
    }
#endif

    // FALLBACK: Create basic UNiagaraSystem object if factory is not available
    UNiagaraSystem* BasicNiagaraSystem = NewObject<UNiagaraSystem>(Package, FName(*FString::Printf(TEXT("%s_System"), *SystemName)), RF_Standalone | RF_Public);
    if (BasicNiagaraSystem)
    {
        // Basic configuration
        BasicNiagaraSystem->PostEditChange();
        Package->MarkPackageDirty();
        FAssetRegistryModule::AssetCreated(BasicNiagaraSystem);

        bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
        if (bSaved)
        {
            UE_LOG(LogTemp, Log, TEXT("CreateRealNiagaraSystemAsset: Created basic Niagara system %s (Type: %s)"), *SystemName, *EffectType);
            return BasicNiagaraSystem;
        }
    }

    UE_LOG(LogTemp, Error, TEXT("CreateRealNiagaraSystemAsset: Failed to create Niagara system asset"));
    return nullptr;
}