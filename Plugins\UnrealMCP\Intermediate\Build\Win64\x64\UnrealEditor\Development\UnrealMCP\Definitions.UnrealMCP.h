// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for UnrealMCP
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"
#undef UNREALMCP_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME AURACRON
#define UE_TARGET_NAME AURACRONEditor
#define UE_MODULE_NAME "UnrealMCP"
#define UE_PLUGIN_NAME "UnrealMCP"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define EDITORSCRIPTINGUTILITIES_API DLLIMPORT
#define STATICMESHEDITOR_API DLLIMPORT
#define LEVELSEQUENCE_API DLLIMPORT
#define PCGCOMPUTE_API DLLIMPORT
#define MESHMODELINGTOOLS_API DLLIMPORT
#define GEOMETRYFRAMEWORK_API DLLIMPORT
#define MESHCONVERSION_API DLLIMPORT
#define GEOMETRYALGORITHMS_API DLLIMPORT
#define DYNAMICMESH_API DLLIMPORT
#define MODELINGCOMPONENTS_API DLLIMPORT
#define MODELINGOPERATORS_API DLLIMPORT
#define TEXTUREUTILITIESCOMMON_API DLLIMPORT
#define MESHMODELINGTOOLSEXP_API DLLIMPORT
#define MODELINGOPERATORSEDITORONLY_API DLLIMPORT
#define BLUEPRINTEDITORLIBRARY_API DLLIMPORT
#define UMGEDITOR_API DLLIMPORT
#define SEQUENCERCORE_API DLLIMPORT
#define CURVEEDITOR_API DLLIMPORT
#define SEQUENCERWIDGETS_API DLLIMPORT
#define SEQUENCER_API DLLIMPORT
#define SCENEOUTLINER_API DLLIMPORT
#define LANDSCAPEEDITOR_API DLLIMPORT
#define WORLDPARTITIONEDITOR_API DLLIMPORT
#define DATALAYEREDITOR_API DLLIMPORT
#define UNREALMCP_API DLLEXPORT
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define AIMODULE_API DLLIMPORT
#define PCG_API DLLIMPORT
#define COMPUTEFRAMEWORK_API DLLIMPORT
#define FOLIAGE_API DLLIMPORT
#define LANDSCAPEPATCH_API DLLIMPORT
#define GEOMETRYFLOWCORE_API DLLIMPORT
#define GEOMETRYFLOWMESHPROCESSING_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define TELEMETRYUTILS_API DLLIMPORT
#define WITH_RPCLIB 1
#define MLADAPTER_API DLLIMPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
