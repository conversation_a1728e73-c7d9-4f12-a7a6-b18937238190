#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Modern UE 5.6.1 Light Component APIs
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/LightComponent.h"

// Modern UE 5.6.1 Sky Atmosphere APIs
#include "Components/SkyAtmosphereComponent.h"
#include "Components/VolumetricCloudComponent.h"
#include "Components/ExponentialHeightFogComponent.h"

// Modern UE 5.6.1 Post Process APIs
#include "Components/PostProcessComponent.h"
#include "Engine/PostProcessVolume.h"

// Modern UE 5.6.1 Niagara APIs - Forward declarations (plugin dependency)
class UNiagaraComponent;
class UNiagaraSystem;
class UNiagaraFunctionLibrary;

// Engine APIs
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "Materials/MaterialInterface.h"

// Editor APIs
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

#include "UnrealMCPVisualEffectsCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 VISUAL EFFECTS STRUCTURES
// ========================================

/**
 * Auracron lighting configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FAuracronLightingConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString LightName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString LightType; // directional, point, spot

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector LightLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FRotator LightRotation = FRotator::ZeroRotator;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor LightColor = FLinearColor::White;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float LightIntensity = 3.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AttenuationRadius = 1000.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseLumen = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bCastShadows = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float DynamicShadowDistance = 20000.0f;

    FAuracronLightingConfig()
    {
        LightName = TEXT("AuracronLight");
        LayerIndex = 0;
        LightType = TEXT("directional");
        LightLocation = FVector::ZeroVector;
        LightRotation = FRotator::ZeroRotator;
        LightColor = FLinearColor::White;
        LightIntensity = 3.0f;
        AttenuationRadius = 1000.0f;
        bUseLumen = true;
        bCastShadows = true;
        DynamicShadowDistance = 20000.0f;
    }
};

/**
 * Sky atmosphere configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FSkyAtmosphereConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString AtmosphereName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float BottomRadius = 6360.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float AtmosphereHeight = 60.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor RayleighScattering = FLinearColor(0.005802f, 0.013558f, 0.033100f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float RayleighExponentialDistribution = 8.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor MieScattering = FLinearColor(0.003996f, 0.003996f, 0.003996f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float MieAnisotropy = 0.8f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor GroundAlbedo = FLinearColor(0.4f, 0.4f, 0.4f, 1.0f);

    FSkyAtmosphereConfig()
    {
        AtmosphereName = TEXT("AuracronAtmosphere");
        BottomRadius = 6360.0f;
        AtmosphereHeight = 60.0f;
        RayleighScattering = FLinearColor(0.005802f, 0.013558f, 0.033100f, 1.0f);
        RayleighExponentialDistribution = 8.0f;
        MieScattering = FLinearColor(0.003996f, 0.003996f, 0.003996f, 1.0f);
        MieAnisotropy = 0.8f;
        GroundAlbedo = FLinearColor(0.4f, 0.4f, 0.4f, 1.0f);
    }
};

/**
 * Volumetric effects configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FVolumetricEffectsConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString EffectName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString EffectType; // fog, clouds, particles

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector EffectLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector EffectScale = FVector(1.0f, 1.0f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor EffectColor = FLinearColor::White;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float EffectDensity = 1.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float EffectIntensity = 1.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseVolumetricLighting = true;

    FVolumetricEffectsConfig()
    {
        EffectName = TEXT("VolumetricEffect");
        EffectType = TEXT("fog");
        EffectLocation = FVector::ZeroVector;
        EffectScale = FVector(1.0f, 1.0f, 1.0f);
        EffectColor = FLinearColor::White;
        EffectDensity = 1.0f;
        EffectIntensity = 1.0f;
        bUseVolumetricLighting = true;
    }
};

/**
 * Niagara effects configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FNiagaraEffectsConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString EffectName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString EffectType; // particles, magic, environmental

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector EffectLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FRotator EffectRotation = FRotator::ZeroRotator;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector EffectScale = FVector(1.0f, 1.0f, 1.0f);

    // Note: Niagara system will be implemented when plugin dependencies are configured
    // TSoftObjectPtr<UNiagaraSystem> NiagaraSystem;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bAutoActivate = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bAutoDestroy = false;

    FNiagaraEffectsConfig()
    {
        EffectName = TEXT("NiagaraEffect");
        EffectType = TEXT("particles");
        EffectLocation = FVector::ZeroVector;
        EffectRotation = FRotator::ZeroRotator;
        EffectScale = FVector(1.0f, 1.0f, 1.0f);
        // NiagaraSystem = nullptr;
        bAutoActivate = true;
        bAutoDestroy = false;
    }
};

/**
 * Post process configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FPostProcessConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString PostProcessName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector VolumeLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector VolumeExtent = FVector(1000.0f, 1000.0f, 1000.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float BloomIntensity = 0.675f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ExposureCompensation = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor ColorGrading = FLinearColor::White;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUnbound = true;

    FPostProcessConfig()
    {
        PostProcessName = TEXT("PostProcessVolume");
        VolumeLocation = FVector::ZeroVector;
        VolumeExtent = FVector(1000.0f, 1000.0f, 1000.0f);
        BloomIntensity = 0.675f;
        ExposureCompensation = 0.0f;
        ColorGrading = FLinearColor::White;
        bUnbound = true;
    }
};

/**
 * UnrealMCP Visual Effects Commands - Modern UE 5.6.1 Implementation
 * Handles advanced lighting, atmosphere, and visual effects with Lumen/Nanite integration
 */
UCLASS()
class UNREALMCP_API UUnrealMCPVisualEffectsCommands : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Handle visual effects command routing
     * @param CommandName Name of the command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with command results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params);

    /**
     * Create dynamic lighting using modern UE 5.6.1 APIs
     * @param Params - Must include:
     *                "light_name" - Name of the light
     *                "light_type" - Type of light (directional, point, spot)
     *                "location" - Light location
     *                "intensity" - Light intensity
     *                "color" - Light color
     *                "layer_index" - Layer index for layer-specific settings
     * @return JSON response with lighting creation results
     */
    TSharedPtr<FJsonObject> HandleCreateDynamicLighting(const TSharedPtr<FJsonObject>& Params);

    /**
     * Setup sky atmosphere using modern APIs
     * @param Params - Must include:
     *                "atmosphere_name" - Name of the atmosphere
     *                "bottom_radius" - Planet bottom radius
     *                "atmosphere_height" - Atmosphere height
     *                "rayleigh_scattering" - Rayleigh scattering color
     *                "mie_scattering" - Mie scattering color
     * @return JSON response with atmosphere setup results
     */
    TSharedPtr<FJsonObject> HandleSetupSkyAtmosphere(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create volumetric effects (fog, clouds)
     * @param Params - Must include:
     *                "effect_name" - Name of the effect
     *                "effect_type" - Type of effect (fog, clouds)
     *                "location" - Effect location
     *                "density" - Effect density
     *                "color" - Effect color
     * @return JSON response with volumetric effects creation results
     */
    TSharedPtr<FJsonObject> HandleCreateVolumetricEffects(const TSharedPtr<FJsonObject>& Params);

    /**
     * Setup Niagara particle systems
     * @param Params - Must include:
     *                "effect_name" - Name of the Niagara effect
     *                "effect_type" - Type of effect (particles, magic, environmental)
     *                "location" - Effect location
     *                "niagara_system" - Path to Niagara system asset
     * @return JSON response with Niagara setup results
     */
    TSharedPtr<FJsonObject> HandleSetupNiagaraEffects(const TSharedPtr<FJsonObject>& Params);

    /**
     * Configure post processing effects
     * @param Params - Must include:
     *                "postprocess_name" - Name of the post process volume
     *                "location" - Volume location
     *                "extent" - Volume extent
     *                "bloom_intensity" - Bloom intensity
     *                "exposure" - Exposure compensation
     * @return JSON response with post process configuration results
     */
    TSharedPtr<FJsonObject> HandleConfigurePostProcessing(const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create robust lighting using modern UE 5.6.1 APIs
     * @param LightingConfig Lighting configuration
     * @return Created light actor
     */
    AActor* CreateRobustLighting(const FAuracronLightingConfig& LightingConfig);

    /**
     * Setup advanced sky atmosphere using modern APIs
     * @param AtmosphereConfig Sky atmosphere configuration
     * @return Created sky atmosphere actor
     */
    AActor* SetupAdvancedSkyAtmosphere(const FSkyAtmosphereConfig& AtmosphereConfig);

    /**
     * Create volumetric effects using modern APIs
     * @param EffectsConfig Volumetric effects configuration
     * @return Created volumetric effects actor
     */
    AActor* CreateVolumetricEffects(const FVolumetricEffectsConfig& EffectsConfig);

    /**
     * Create real Niagara System asset using modern UE 5.6.1 APIs
     * @param SystemName Name of the Niagara system
     * @param EffectType Type of effect (particles, magic, environmental)
     * @return Created Niagara System asset
     */
    UNiagaraSystem* CreateRealNiagaraSystemAsset(const FString& SystemName, const FString& EffectType);

    /**
     * Setup Niagara particle system using modern APIs
     * @param NiagaraConfig Niagara effects configuration
     * @return Created Niagara actor
     */
    AActor* SetupNiagaraParticleSystem(const FNiagaraEffectsConfig& NiagaraConfig);

    /**
     * Configure post processing volume using modern APIs
     * @param PostProcessConfig Post process configuration
     * @return Created post process volume
     */
    APostProcessVolume* ConfigurePostProcessVolume(const FPostProcessConfig& PostProcessConfig);

    /**
     * Get layer-specific lighting settings
     * @param LayerIndex Layer index
     * @return Lighting configuration for the layer
     */
    FAuracronLightingConfig GetLayerLightingSettings(int32 LayerIndex);

    /**
     * CORREÇÃO ROBUSTA: Get Niagara system path for effect type
     * @param EffectType Type of effect (fire, smoke, magic, etc.)
     * @return Path to appropriate Niagara system
     */
    FString GetNiagaraSystemPathForType(const FString& EffectType);

    /**
     * CORREÇÃO ROBUSTA: Get particle system path for effect type (fallback)
     * @param EffectType Type of effect
     * @return Path to appropriate particle system
     */
    FString GetParticleSystemPathForType(const FString& EffectType);

    /**
     * CORREÇÃO ROBUSTA: Get basic mesh path for effect type (final fallback)
     * @param EffectType Type of effect
     * @return Path to appropriate basic mesh
     */
    FString GetBasicMeshPathForEffectType(const FString& EffectType);

private:
    // Cache for created lights
    UPROPERTY()
    TMap<FString, TObjectPtr<AActor>> CreatedLights;

    // Cache for sky atmosphere components
    UPROPERTY()
    TMap<FString, TObjectPtr<USkyAtmosphereComponent>> SkyAtmosphereComponents;

    // Cache for volumetric effects
    UPROPERTY()
    TMap<FString, TObjectPtr<AActor>> VolumetricEffects;

    // Cache for Niagara components - Note: Will be implemented when plugin dependencies are configured
    // UPROPERTY()
    // TMap<FString, TObjectPtr<UNiagaraComponent>> NiagaraComponents;

    // Cache for post process volumes
    UPROPERTY()
    TMap<FString, TObjectPtr<APostProcessVolume>> PostProcessVolumes;
};
