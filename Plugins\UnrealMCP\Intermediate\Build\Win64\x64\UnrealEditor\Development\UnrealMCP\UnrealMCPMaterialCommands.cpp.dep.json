{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpmaterialcommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpmaterialcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialinstanceconstant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialinstanceconstant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialparametercollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialparametercollection.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialparametercollectioninstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialparametercollectioninstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialfunctionmateriallayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialfunctioninstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialfunctioninstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialfunctionmateriallayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texture2ddynamic.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\texture2ddynamic.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texturerendertarget2d.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\texturerendertarget.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\texturerendertarget.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\texturerendertarget2d.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionconstant.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionconstant.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionconstant3vector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionconstant3vector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionconstant4vector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionconstant4vector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressiontexturesample.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressiontexturebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressiontexturebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressiontexturesample.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionmultiply.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionmultiply.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionadd.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionadd.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\source\\editorscriptingutilities\\public\\editorassetlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\intermediate\\build\\win64\\unrealeditor\\inc\\editorscriptingutilities\\uht\\editorassetlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\materialfactorynew.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\materialfactorynew.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\materialinstanceconstantfactorynew.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\materialinstanceconstantfactorynew.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\materialfunctionfactorynew.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\materialfunctionfactorynew.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpmaterialcommands.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcommonutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionlinearinterpolate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionlinearinterpolate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\materials\\materialexpressionfresnel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialexpressionfresnel.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\materialparametercollectionfactorynew.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\materialparametercollectionfactorynew.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\texturefactory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\importsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\texturefactory.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}